// ===== إدارة صفحة العملاء والموردين =====

// بيانات وهمية للعملاء
const customersData = {
  totalCustomers: 342,
  vipCustomers: 28,
  averagePurchases: 2450,
  inactiveCustomers: 23,
  customers: [
    {
      id: 'CUST-001',
      name: 'محمد أحمد السعيد',
      type: 'عميل مميز',
      phone: '0501234567',
      email: '<EMAIL>',
      city: 'الرياض',
      totalPurchases: 15750,
      invoicesCount: 23,
      lastVisit: 'اليوم',
      status: 'active'
    },
    {
      id: 'CUST-002',
      name: 'سعد الأحمد',
      type: 'عميل عادي',
      phone: '0509876543',
      email: '<EMAIL>',
      city: 'جدة',
      totalPurchases: 8920,
      invoicesCount: 12,
      lastVisit: 'أمس',
      status: 'active'
    },
    {
      id: 'CUST-003',
      name: 'عبدالله محمد',
      type: 'عميل تجاري',
      phone: '0551122334',
      email: 'abdu<PERSON>@company.com',
      city: 'الدمام',
      totalPurchases: 25340,
      invoicesCount: 45,
      lastVisit: 'منذ 3 أيام',
      status: 'inactive'
    }
  ]
};

// ===== تهيئة صفحة العملاء =====
document.addEventListener('DOMContentLoaded', function() {
  initializeCustomersPage();
});

function initializeCustomersPage() {
  updateCustomersStats();
  setupEventListeners();
  loadCustomersTable();

  console.log('👥 تم تهيئة صفحة العملاء');
}

// ===== تحديث الإحصائيات =====
function updateCustomersStats() {
  updateStatCard('total-customers', customersData.totalCustomers);
  updateStatCard('vip-customers', customersData.vipCustomers);
  updateStatCard('average-purchases', customersData.averagePurchases, 'ريال');
  updateStatCard('inactive-customers', customersData.inactiveCustomers);
}

function updateStatCard(cardType, value, suffix = '') {
  const statValue = document.querySelector(`[data-stat="${cardType}"] .stat-value`);
  if (statValue) {
    if (suffix === 'ريال') {
      statValue.textContent = `${value.toLocaleString('ar-SA')} ${suffix}`;
    } else {
      statValue.textContent = value.toLocaleString('ar-SA');
    }
  }
}

// ===== إعداد مستمعي الأحداث =====
function setupEventListeners() {
  // أزرار التبويبات
  const tabButtons = document.querySelectorAll('.tab-btn');
  tabButtons.forEach(button => {
    button.addEventListener('click', handleTabSwitch);
  });

  // أزرار العرض
  const viewButtons = document.querySelectorAll('.view-btn');
  viewButtons.forEach(button => {
    button.addEventListener('click', switchView);
  });

  // مربع البحث
  const searchInput = document.querySelector('.search-input input');
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }

  // فلاتر التصفية
  const filterSelects = document.querySelectorAll('.customers-filters select');
  filterSelects.forEach(select => {
    select.addEventListener('change', applyFilters);
  });
}

// ===== تحميل جدول العملاء =====
function loadCustomersTable() {
  const tableBody = document.querySelector('.customers-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  customersData.customers.forEach(customer => {
    const row = createCustomerRow(customer);
    tableBody.appendChild(row);
  });
}

function createCustomerRow(customer) {
  const row = document.createElement('tr');
  row.innerHTML = `
    <td>
      <div class="customer-profile">
        <div class="customer-avatar">
          <span class="material-icons">person</span>
        </div>
        <div class="customer-details">
          <span class="customer-name">${customer.name}</span>
          <span class="customer-type">${customer.type}</span>
        </div>
      </div>
    </td>
    <td>
      <div class="contact-info">
        <span class="phone">${customer.phone}</span>
        <span class="email">${customer.email}</span>
      </div>
    </td>
    <td>${customer.city}</td>
    <td>
      <span class="total-purchases">${customer.totalPurchases.toLocaleString('ar-SA')} ريال</span>
      <span class="purchases-count">${customer.invoicesCount} فاتورة</span>
    </td>
    <td>${customer.lastVisit}</td>
    <td>
      <span class="status-badge ${customer.status}">${getStatusText(customer.status)}</span>
    </td>
    <td>
      <div class="action-buttons">
        <button class="btn-icon" title="عرض" onclick="viewCustomer('${customer.id}')">
          <span class="material-icons">visibility</span>
        </button>
        <button class="btn-icon" title="تعديل" onclick="editCustomer('${customer.id}')">
          <span class="material-icons">edit</span>
        </button>
        <button class="btn-icon" title="فواتير" onclick="viewCustomerInvoices('${customer.id}')">
          <span class="material-icons">receipt_long</span>
        </button>
        <button class="btn-icon" title="المزيد" onclick="showCustomerMenu('${customer.id}')">
          <span class="material-icons">more_vert</span>
        </button>
      </div>
    </td>
  `;

  return row;
}

function getStatusText(status) {
  const statusTexts = {
    active: 'نشط',
    inactive: 'غير نشط',
    blocked: 'محظور'
  };
  return statusTexts[status] || status;
}

// ===== إدارة التبويبات =====
function handleTabSwitch(event) {
  const tabType = event.currentTarget.getAttribute('data-tab');

  // إزالة الفئة النشطة من جميع التبويبات
  document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.classList.remove('active');
  });

  // إضافة الفئة النشطة للتبويب المحدد
  event.currentTarget.classList.add('active');

  switchTab(tabType);
}

function switchTab(tabType) {
  let filteredData = [];

  switch(tabType) {
    case 'customers':
      filteredData = customersData.customers.filter(c => c.type !== 'مورد');
      break;
    case 'suppliers':
      // سيتم إضافة بيانات الموردين لاحقاً
      filteredData = [];
      break;
    case 'vip':
      filteredData = customersData.customers.filter(c => c.type === 'عميل مميز');
      break;
    default:
      filteredData = customersData.customers;
  }

  updateCustomersTable(filteredData);
  showNotification(`تم التبديل إلى تبويب ${getTabText(tabType)}`, 'success');
}

function getTabText(tabType) {
  const tabTexts = {
    customers: 'العملاء',
    suppliers: 'الموردين',
    vip: 'العملاء المميزين'
  };
  return tabTexts[tabType] || tabType;
}

// ===== وظائف الفلترة والبحث =====
function applyFilters() {
  const cityFilter = document.querySelector('select[name="city"]')?.value;
  const typeFilter = document.querySelector('select[name="type"]')?.value;
  const statusFilter = document.querySelector('select[name="status"]')?.value;

  let filteredCustomers = [...customersData.customers];

  if (cityFilter && cityFilter !== 'جميع المناطق') {
    filteredCustomers = filteredCustomers.filter(customer => customer.city === cityFilter);
  }

  if (typeFilter && typeFilter !== 'جميع الأنواع') {
    filteredCustomers = filteredCustomers.filter(customer => customer.type === typeFilter);
  }

  if (statusFilter && statusFilter !== 'جميع الحالات') {
    filteredCustomers = filteredCustomers.filter(customer => {
      return getStatusText(customer.status) === statusFilter;
    });
  }

  updateCustomersTable(filteredCustomers);
  showNotification('تم تطبيق الفلتر بنجاح', 'success');
}

function handleSearch(event) {
  const searchTerm = event.target.value.toLowerCase();

  const filteredCustomers = customersData.customers.filter(customer => {
    return customer.name.toLowerCase().includes(searchTerm) ||
           customer.phone.includes(searchTerm) ||
           customer.email.toLowerCase().includes(searchTerm);
  });

  updateCustomersTable(filteredCustomers);
}

function updateCustomersTable(customers) {
  const tableBody = document.querySelector('.customers-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  customers.forEach(customer => {
    const row = createCustomerRow(customer);
    tableBody.appendChild(row);
  });

  // تحديث عدد النتائج
  updateResultsCount(customers.length);
}

function updateResultsCount(count) {
  const paginationInfo = document.querySelector('.pagination-info');
  if (paginationInfo) {
    paginationInfo.textContent = `عرض ${count} من ${customersData.customers.length}`;
  }
}

// ===== تبديل طريقة العرض =====
function switchView(event) {
  const viewType = event.currentTarget.getAttribute('data-view');

  // إزالة الفئة النشطة من جميع الأزرار
  document.querySelectorAll('.view-btn').forEach(btn => {
    btn.classList.remove('active');
  });

  // إضافة الفئة النشطة للزر المحدد
  event.currentTarget.classList.add('active');

  if (viewType === 'cards') {
    showCardsView();
  } else {
    showTableView();
  }

  showNotification(`تم التبديل إلى عرض ${viewType === 'cards' ? 'البطاقات' : 'الجدول'}`, 'info');
}

function showTableView() {
  const tableContainer = document.querySelector('.customers-table-container');
  if (tableContainer) {
    tableContainer.style.display = 'block';
  }
}

function showCardsView() {
  // سيتم تنفيذها لاحقاً
  showNotification('عرض البطاقات قيد التطوير', 'info');
}

// ===== وظائف الإجراءات =====
function addNewCustomer() {
  console.log('🔄 محاولة فتح نافذة إضافة العميل...');

  // التحقق من وجود النافذة
  if (window.customerModal && typeof window.customerModal.show === 'function') {
    console.log('✅ تم العثور على نافذة العملاء، جاري فتحها...');
    window.customerModal.show();
  } else {
    console.log('⚠️ نافذة العملاء غير متاحة، إنشاء نافذة بديلة...');
    createSimpleCustomerModal();
  }
}

// إنشاء نافذة بسيطة للعملاء
function createSimpleCustomerModal() {
  // إزالة أي نافذة موجودة
  const existingModal = document.getElementById('simpleCustomerModal');
  if (existingModal) {
    existingModal.remove();
  }

  // إنشاء النافذة
  const modalHTML = `
    <div id="simpleCustomerModal" class="modal-overlay show">
      <div class="modal-container">
        <div class="modal-header">
          <h3>إضافة عميل جديد</h3>
          <button class="modal-close" onclick="closeSimpleCustomerModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="modal-body">
          <form id="simpleCustomerForm">
            <div class="form-group">
              <label class="form-label">الاسم الثلاثي *</label>
              <input type="text" name="fullName" class="form-control" placeholder="الاسم الأول الأوسط الأخير" required>
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهوية الوطنية العراقية *</label>
              <input type="text" name="idNumber" class="form-control" placeholder="رقم الهوية الوطنية العراقية (12 رقم)" required maxlength="12">
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهاتف *</label>
              <input type="tel" name="phone" class="form-control" placeholder="07xxxxxxxxx" required>
            </div>

            <div class="form-group">
              <label class="form-label">المحافظة *</label>
              <select name="region" class="form-control" required>
                <option value="">اختر المحافظة</option>
                <option value="بغداد">بغداد</option>
                <option value="البصرة">البصرة</option>
                <option value="نينوى">نينوى</option>
                <option value="أربيل">أربيل</option>
                <option value="النجف">النجف</option>
                <option value="كربلاء">كربلاء</option>
                <option value="بابل">بابل</option>
                <option value="ديالى">ديالى</option>
                <option value="الأنبار">الأنبار</option>
                <option value="كركوك">كركوك</option>
                <option value="صلاح الدين">صلاح الدين</option>
                <option value="واسط">واسط</option>
                <option value="ذي قار">ذي قار</option>
                <option value="المثنى">المثنى</option>
                <option value="القادسية">القادسية</option>
                <option value="ميسان">ميسان</option>
                <option value="دهوك">دهوك</option>
                <option value="السليمانية">السليمانية</option>
                <option value="حلبجة">حلبجة</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">نوع العميل</label>
              <select name="type" class="form-control">
                <option value="individual">فرد</option>
                <option value="company">شركة</option>
              </select>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="closeSimpleCustomerModal()">
            إلغاء
          </button>
          <button type="button" class="btn btn-primary" onclick="saveSimpleCustomer()">
            <span class="material-icons">save</span>
            حفظ العميل
          </button>
        </div>
      </div>
    </div>
  `;

  // إضافة النافذة للصفحة
  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // إضافة الأنماط
  addCustomerModalStyles();

  console.log('✅ تم إنشاء نافذة العميل البديلة');
}

async function editCustomer(customerId) {
  try {
    if (window.customerModal && window.DatabaseAPI) {
      const customer = await window.DatabaseAPI.getCustomerById(customerId);
      if (customer) {
        window.customerModal.show(customer);
      } else {
        showNotification('لم يتم العثور على العميل', 'error');
      }
    } else {
      showNotification('نافذة تعديل العميل غير متاحة', 'error');
    }
  } catch (error) {
    console.error('خطأ في تحميل بيانات العميل:', error);
    showNotification('حدث خطأ في تحميل بيانات العميل', 'error');
  }
}

async function deleteCustomer(customerId) {
  try {
    if (window.deleteModal && window.DatabaseAPI) {
      // جلب بيانات العميل للحصول على الاسم
      const customer = await window.DatabaseAPI.getCustomerById(customerId);
      const customerName = customer ? customer.name : `العميل ${customerId}`;

      window.deleteModal.showDeleteCustomer(customerName, customerId, async () => {
        try {
          await window.DatabaseAPI.deleteCustomer(customerId);
          showNotification('تم حذف العميل بنجاح', 'success');
          // إعادة تحميل الصفحة
          window.location.reload();
        } catch (error) {
          console.error('خطأ في حذف العميل:', error);
          showNotification('حدث خطأ في حذف العميل', 'error');
        }
      });
    } else {
      // طريقة بديلة
      if (confirm(`هل أنت متأكد من حذف العميل ${customerId}؟`)) {
        showNotification(`تم حذف العميل ${customerId}`, 'success');
      }
    }
  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    showNotification('حدث خطأ في حذف العميل', 'error');
  }
}

// إغلاق النافذة البسيطة
function closeSimpleCustomerModal() {
  const modal = document.getElementById('simpleCustomerModal');
  if (modal) {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.remove();
    }, 300);
  }
}

// حفظ العميل من النافذة البسيطة
async function saveSimpleCustomer() {
  const form = document.getElementById('simpleCustomerForm');
  if (!form) return;

  // جمع البيانات
  const formData = new FormData(form);
  const customerData = {
    id: Date.now().toString(),
    fullName: formData.get('fullName'),
    idNumber: formData.get('idNumber'),
    phone: formData.get('phone'),
    region: formData.get('region'),
    type: formData.get('type'),
    createdAt: new Date().toISOString(),
    lastVisit: new Date().toISOString()
  };

  // التحقق من البيانات المطلوبة
  if (!customerData.fullName || !customerData.idNumber || !customerData.phone || !customerData.region) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // التحقق من صحة رقم الهاتف العراقي
  const phoneRegex = /^07\d{9}$/;
  if (!phoneRegex.test(customerData.phone)) {
    showNotification('يرجى إدخال رقم هاتف عراقي صحيح (07xxxxxxxxx)', 'error');
    return;
  }

  // التحقق من صحة رقم الهوية العراقية
  const idRegex = /^\d{12}$/;
  if (!idRegex.test(customerData.idNumber)) {
    showNotification('يرجى إدخال رقم هوية وطنية عراقية صحيح (12 رقم)', 'error');
    return;
  }

  try {
    // إضافة العميل إلى قائمة العملاء
    addCustomerToList(customerData);

    showNotification('تم إضافة العميل بنجاح', 'success');
    closeSimpleCustomerModal();

    // تحديث عدد العملاء
    updateCustomersCount();

  } catch (error) {
    console.error('خطأ في حفظ العميل:', error);
    showNotification('حدث خطأ في حفظ العميل', 'error');
  }
}

// إضافة أنماط النافذة
function addCustomerModalStyles() {
  // التحقق من وجود الأنماط
  if (document.getElementById('customerModalStyles')) return;

  const styles = document.createElement('style');
  styles.id = 'customerModalStyles';
  styles.textContent = `
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .modal-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .modal-container {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      transform: translateY(-20px);
      transition: transform 0.3s ease;
    }

    .modal-overlay.show .modal-container {
      transform: translateY(0);
    }

    .modal-header {
      padding: 20px 24px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .modal-header h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .modal-close {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 50%;
      cursor: pointer;
      color: #666;
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: #f5f5f5;
      color: #333;
    }

    .modal-body {
      padding: 24px;
    }

    .modal-footer {
      padding: 20px 24px;
      border-top: 1px solid #eee;
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-label {
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .form-control {
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-outline {
      background: transparent;
      color: #666;
      border: 1px solid #ddd;
    }

    .btn-outline:hover {
      background: #f8f9fa;
      border-color: #adb5bd;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }

      .modal-container {
        width: 95%;
        margin: 20px;
      }
    }
  `;

  document.head.appendChild(styles);
}

// ===== وظائف مساعدة =====
function showNotification(message, type = 'info') {
  // إنشاء إشعار بسيط
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'}-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-weight: 500;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// ===== تصدير الوظائف =====
window.CustomersPage = {
  addNewCustomer,
  editCustomer,
  deleteCustomer,
  switchTab
};

// قائمة العملاء
let customersList = [
  {
    id: '1',
    fullName: 'محمد أحمد حسين',
    idNumber: '123456789012',
    phone: '07901234567',
    region: 'بغداد',
    type: 'individual',
    createdAt: new Date().toISOString(),
    lastVisit: new Date().toISOString(),
    totalDebt: 150000,
    totalPurchases: 8,
    invoices: [
      {
        id: 'INV-20241201-001',
        date: '2024-12-01',
        total: 75000,
        paid: 0,
        remaining: 75000,
        status: 'unpaid',
        items: [
          { name: 'مفك كهربائي', quantity: 2, price: 25000, total: 50000 },
          { name: 'مفتاح كهربائي', quantity: 3, price: 8000, total: 24000 }
        ],
        notes: 'فاتورة آجلة - دفع خلال أسبوع'
      },
      {
        id: 'INV-20241128-002',
        date: '2024-11-28',
        total: 85000,
        paid: 10000,
        remaining: 75000,
        status: 'partial',
        items: [
          { name: 'مثقاب كهربائي', quantity: 1, price: 85000, total: 85000 }
        ],
        notes: ''
      }
    ],
    payments: [
      {
        id: 'PAY-001',
        date: '2024-11-30',
        amount: 10000,
        invoiceId: 'INV-20241128-002',
        notes: 'دفعة جزئية'
      }
    ]
  },
  {
    id: '2',
    fullName: 'علي عبدالله كريم',
    idNumber: '987654321098',
    phone: '07801234567',
    region: 'البصرة',
    type: 'individual',
    createdAt: new Date().toISOString(),
    lastVisit: new Date().toISOString(),
    totalDebt: 0,
    totalPurchases: 3,
    invoices: [
      {
        id: 'INV-20241125-003',
        date: '2024-11-25',
        total: 45000,
        paid: 45000,
        remaining: 0,
        status: 'paid',
        items: [
          { name: 'كابل كهربائي', quantity: 3, price: 15000, total: 45000 }
        ],
        notes: ''
      }
    ],
    payments: [
      {
        id: 'PAY-002',
        date: '2024-11-25',
        amount: 45000,
        invoiceId: 'INV-20241125-003',
        notes: 'دفع كامل'
      }
    ]
  },
  {
    id: '3',
    fullName: 'شركة البناء العراقية',
    idNumber: '112233445566',
    phone: '07701234567',
    region: 'أربيل',
    type: 'company',
    createdAt: new Date().toISOString(),
    lastVisit: new Date().toISOString(),
    totalDebt: 250000,
    totalPurchases: 15,
    invoices: [
      {
        id: 'INV-20241130-004',
        date: '2024-11-30',
        total: 250000,
        paid: 0,
        remaining: 250000,
        status: 'unpaid',
        items: [
          { name: 'أسمنت', quantity: 10, price: 12000, total: 120000 },
          { name: 'طوب أحمر', quantity: 260, price: 500, total: 130000 }
        ],
        notes: 'طلبية كبيرة - دفع خلال شهر'
      }
    ],
    payments: []
  }
];

// إضافة عميل إلى القائمة
function addCustomerToList(customer) {
  customersList.unshift(customer);
  renderCustomers();
}

// عرض العملاء
function renderCustomers(customers = customersList) {
  const grid = document.getElementById('customersGrid');
  if (!grid) return;

  if (customers.length === 0) {
    grid.innerHTML = `
      <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #7f8c8d;">
        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px;">people_outline</span>
        <p>لا توجد عملاء حالياً</p>
        <button class="btn btn-primary" onclick="addNewCustomer()">إضافة أول عميل</button>
      </div>
    `;
    return;
  }

  grid.innerHTML = customers.map(customer => `
    <div class="customer-card ${customer.totalDebt > 0 ? 'has-debt' : ''}" onclick="openCustomerRecord('${customer.id}')">
      <div class="customer-header">
        <div class="customer-avatar">
          <span class="material-icons">${customer.type === 'company' ? 'business' : 'person'}</span>
          ${customer.totalDebt > 0 ? '<span class="debt-indicator material-icons">warning</span>' : ''}
        </div>
        <div class="customer-info">
          <h3>${customer.fullName}</h3>
          <span class="customer-type">${customer.type === 'company' ? 'شركة' : 'فرد'}</span>
        </div>
      </div>

      <div class="customer-details">
        <div class="customer-detail">
          <span class="material-icons">phone</span>
          <span>${customer.phone}</span>
        </div>
        <div class="customer-detail">
          <span class="material-icons">location_on</span>
          <span>${customer.region}</span>
        </div>
        <div class="customer-detail debt-info">
          <span class="material-icons">account_balance_wallet</span>
          <span class="debt-amount ${customer.totalDebt > 0 ? 'has-debt' : 'no-debt'}">
            ${customer.totalDebt > 0 ? formatPrice(customer.totalDebt) + ' دين' : 'لا يوجد دين'}
          </span>
        </div>
        <div class="customer-detail">
          <span class="material-icons">shopping_cart</span>
          <span>${customer.totalPurchases || 0} مشترى</span>
        </div>
      </div>

      <div class="customer-actions" onclick="event.stopPropagation()">
        <button class="btn-icon edit-btn" title="تعديل" onclick="editCustomer('${customer.id}')">
          <span class="material-icons">edit</span>
        </button>
        ${customer.totalDebt > 0 ? `
          <button class="btn-icon payment-btn" title="تسديد" onclick="openPaymentModal('${customer.id}')">
            <span class="material-icons">payment</span>
          </button>
        ` : ''}
        <button class="btn-icon delete-btn ${customer.totalDebt > 0 ? 'disabled' : ''}"
                title="${customer.totalDebt > 0 ? 'لا يمكن الحذف - يوجد دين' : 'حذف'}"
                onclick="deleteCustomer('${customer.id}')"
                ${customer.totalDebt > 0 ? 'disabled' : ''}>
          <span class="material-icons">delete</span>
        </button>
      </div>
    </div>
  `).join('');
}

// حساب الوقت المنقضي
function getTimeAgo(dateString) {
  const now = new Date();
  const date = new Date(dateString);
  const diffInMinutes = Math.floor((now - date) / (1000 * 60));

  if (diffInMinutes < 1) return 'الآن';
  if (diffInMinutes < 60) return `${diffInMinutes} دقيقة`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ساعة`;
  return `${Math.floor(diffInMinutes / 1440)} يوم`;
}

// تحديث عدد العملاء
function updateCustomersCount() {
  const countElement = document.getElementById('customersCount');
  if (countElement) {
    countElement.textContent = `${customersList.length} عميل`;
  }
}

// البحث في العملاء
function searchCustomers() {
  const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
  const filtered = customersList.filter(customer =>
    customer.fullName.toLowerCase().includes(searchTerm) ||
    customer.phone.includes(searchTerm) ||
    customer.idNumber.includes(searchTerm) ||
    customer.region.toLowerCase().includes(searchTerm)
  );
  renderCustomers(filtered);
}

// فلترة العملاء
function filterCustomers() {
  const filterValue = document.getElementById('customerFilter').value;
  let filtered = customersList;

  if (filterValue !== 'all') {
    filtered = customersList.filter(customer => customer.type === filterValue);
  }

  renderCustomers(filtered);
}

// حذف عميل
function deleteCustomer(customerId) {
  const customer = customersList.find(c => c.id === customerId);

  if (!customer) {
    showNotification('العميل غير موجود', 'error');
    return;
  }

  if (customer.totalDebt > 0) {
    showNotification('لا يمكن حذف العميل - يوجد دين مستحق', 'error');
    return;
  }

  if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
    customersList = customersList.filter(customer => customer.id !== customerId);
    renderCustomers();
    updateCustomersCount();
    showNotification('تم حذف العميل بنجاح', 'success');
  }
}

// تم حذف وظيفة openCustomerDetails - الآن ننتقل مباشرة لصفحة سجل العميل

// عرض فواتير العميل
function renderCustomerInvoices(invoices) {
  if (!invoices || invoices.length === 0) {
    return '<p class="no-invoices">لا توجد فواتير لهذا العميل</p>';
  }

  return invoices.map(invoice => `
    <div class="invoice-card ${invoice.status}" onclick="openInvoiceDetails('${invoice.id}')">
      <div class="invoice-header">
        <span class="invoice-number">${invoice.id}</span>
        <span class="invoice-status ${invoice.status}">
          ${invoice.status === 'paid' ? 'مدفوعة' : invoice.status === 'partial' ? 'مدفوعة جزئياً' : 'غير مدفوعة'}
        </span>
      </div>
      <div class="invoice-details">
        <div class="invoice-date">
          <span class="material-icons">date_range</span>
          ${new Date(invoice.date).toLocaleDateString('ar-IQ')}
        </div>
        <div class="invoice-amount">
          <span class="total-amount">${formatPrice(invoice.total)}</span>
          ${invoice.remaining > 0 ? `<span class="remaining-amount">متبقي: ${formatPrice(invoice.remaining)}</span>` : ''}
        </div>
      </div>
      ${invoice.notes ? `<div class="invoice-notes">${invoice.notes}</div>` : ''}
    </div>
  `).join('');
}

// تحديث عميل
function editCustomer(customerId) {
  const customer = customersList.find(c => c.id === customerId);
  if (customer) {
    // فتح نافذة التعديل مع البيانات المحملة مسبقاً
    createSimpleCustomerModal();

    // ملء البيانات
    setTimeout(() => {
      const form = document.getElementById('simpleCustomerForm');
      if (form) {
        form.fullName.value = customer.fullName;
        form.idNumber.value = customer.idNumber;
        form.phone.value = customer.phone;
        form.region.value = customer.region;
        form.type.value = customer.type;

        // تغيير النص والوظيفة
        const title = document.querySelector('#simpleCustomerModal .modal-header h3');
        const saveBtn = document.querySelector('#simpleCustomerModal .btn-primary');

        if (title) title.textContent = 'تعديل بيانات العميل';
        if (saveBtn) {
          saveBtn.onclick = () => updateCustomer(customerId);
        }
      }
    }, 100);
  }
}

// تحديث بيانات العميل
function updateCustomer(customerId) {
  const form = document.getElementById('simpleCustomerForm');
  if (!form) return;

  const formData = new FormData(form);
  const updatedData = {
    fullName: formData.get('fullName'),
    idNumber: formData.get('idNumber'),
    phone: formData.get('phone'),
    region: formData.get('region'),
    type: formData.get('type')
  };

  // التحقق من البيانات
  if (!updatedData.fullName || !updatedData.idNumber || !updatedData.phone || !updatedData.region) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // تحديث العميل في القائمة
  const customerIndex = customersList.findIndex(c => c.id === customerId);
  if (customerIndex !== -1) {
    customersList[customerIndex] = { ...customersList[customerIndex], ...updatedData };
    renderCustomers();
    closeSimpleCustomerModal();
    showNotification('تم تحديث بيانات العميل بنجاح', 'success');
  }
}

// تنسيق السعر
function formatPrice(price) {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price) + ' د.ع';
}

// تهيئة الصفحة
function initCustomersPage() {
  renderCustomers();
  updateCustomersCount();
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initCustomersPage);

// فتح نافذة تفاصيل الفاتورة
function openInvoiceDetails(invoiceId) {
  // البحث عن الفاتورة في جميع العملاء
  let invoice = null;
  let customer = null;

  for (const c of customersList) {
    const foundInvoice = c.invoices?.find(inv => inv.id === invoiceId);
    if (foundInvoice) {
      invoice = foundInvoice;
      customer = c;
      break;
    }
  }

  if (!invoice || !customer) return;

  const modalHTML = `
    <div id="invoiceDetailsModal" class="modal-overlay show">
      <div class="modal-container">
        <div class="modal-header">
          <h3>تفاصيل الفاتورة - ${invoice.id}</h3>
          <button class="modal-close" onclick="closeInvoiceDetailsModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="modal-body">
          <div class="invoice-info">
            <div class="invoice-customer">
              <h4>معلومات العميل</h4>
              <p><strong>الاسم:</strong> ${customer.fullName}</p>
              <p><strong>الهاتف:</strong> ${customer.phone}</p>
            </div>

            <div class="invoice-summary">
              <h4>ملخص الفاتورة</h4>
              <div class="summary-row">
                <span>تاريخ الفاتورة:</span>
                <span>${new Date(invoice.date).toLocaleDateString('ar-IQ')}</span>
              </div>
              <div class="summary-row">
                <span>المجموع الكلي:</span>
                <span>${formatPrice(invoice.total)}</span>
              </div>
              <div class="summary-row">
                <span>المبلغ المدفوع:</span>
                <span>${formatPrice(invoice.paid)}</span>
              </div>
              <div class="summary-row">
                <span>المبلغ المتبقي:</span>
                <span class="${invoice.remaining > 0 ? 'debt-amount' : 'paid-amount'}">${formatPrice(invoice.remaining)}</span>
              </div>
              <div class="summary-row">
                <span>حالة الفاتورة:</span>
                <span class="status ${invoice.status}">
                  ${invoice.status === 'paid' ? 'مدفوعة' : invoice.status === 'partial' ? 'مدفوعة جزئياً' : 'غير مدفوعة'}
                </span>
              </div>
            </div>

            <div class="invoice-items">
              <h4>المنتجات</h4>
              <div class="items-list">
                ${invoice.items.map(item => `
                  <div class="item-row">
                    <span class="item-name">${item.name}</span>
                    <span class="item-qty">×${item.quantity}</span>
                    <span class="item-price">${formatPrice(item.price)}</span>
                    <span class="item-total">${formatPrice(item.total)}</span>
                  </div>
                `).join('')}
              </div>
            </div>

            ${invoice.notes ? `
              <div class="invoice-notes">
                <h4>ملاحظات</h4>
                <p>${invoice.notes}</p>
              </div>
            ` : ''}
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="closeInvoiceDetailsModal()">
            إغلاق
          </button>
          ${invoice.remaining > 0 ? `
            <button type="button" class="btn btn-primary" onclick="openPaymentModal('${customer.id}', '${invoice.id}')">
              <span class="material-icons">payment</span>
              تسديد
            </button>
          ` : ''}
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  addInvoiceDetailsStyles();
}

// فتح نافذة التسديد
function openPaymentModal(customerId, invoiceId = null) {
  const customer = customersList.find(c => c.id === customerId);
  if (!customer) return;

  const modalHTML = `
    <div id="paymentModal" class="modal-overlay show">
      <div class="modal-container">
        <div class="modal-header">
          <h3>تسديد مبلغ - ${customer.fullName}</h3>
          <button class="modal-close" onclick="closePaymentModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="modal-body">
          <div class="payment-info">
            <div class="customer-debt">
              <h4>معلومات الدين</h4>
              <p><strong>إجمالي الدين:</strong> ${formatPrice(customer.totalDebt)}</p>
            </div>

            <div class="payment-form">
              <div class="form-group">
                <label>مبلغ التسديد *</label>
                <input type="number" id="paymentAmount" min="1" max="${customer.totalDebt}" placeholder="أدخل المبلغ">
              </div>

              ${invoiceId ? `
                <div class="form-group">
                  <label>الفاتورة المحددة:</label>
                  <span>${invoiceId}</span>
                  <input type="hidden" id="selectedInvoiceId" value="${invoiceId}">
                </div>
              ` : `
                <div class="form-group">
                  <label>اختر الفاتورة (اختياري)</label>
                  <select id="selectedInvoiceId">
                    <option value="">توزيع تلقائي على الفواتير</option>
                    ${customer.invoices?.filter(inv => inv.remaining > 0).map(inv => `
                      <option value="${inv.id}">${inv.id} - متبقي: ${formatPrice(inv.remaining)}</option>
                    `).join('') || ''}
                  </select>
                </div>
              `}

              <div class="form-group">
                <label>ملاحظات</label>
                <textarea id="paymentNotes" placeholder="ملاحظات إضافية (اختياري)"></textarea>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="closePaymentModal()">
            إلغاء
          </button>
          <button type="button" class="btn btn-success" onclick="processPayment('${customerId}')">
            <span class="material-icons">payment</span>
            تسديد
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  addPaymentModalStyles();
}

// معالجة التسديد
function processPayment(customerId) {
  const customer = customersList.find(c => c.id === customerId);
  if (!customer) return;

  const amount = parseFloat(document.getElementById('paymentAmount').value);
  const invoiceId = document.getElementById('selectedInvoiceId').value;
  const notes = document.getElementById('paymentNotes').value;

  if (!amount || amount <= 0) {
    showNotification('يرجى إدخال مبلغ صحيح', 'error');
    return;
  }

  if (amount > customer.totalDebt) {
    showNotification('المبلغ أكبر من إجمالي الدين', 'error');
    return;
  }

  // إنشاء سجل التسديد
  const payment = {
    id: `PAY-${Date.now()}`,
    date: new Date().toISOString(),
    amount: amount,
    invoiceId: invoiceId || 'توزيع تلقائي',
    notes: notes
  };

  // إضافة التسديد لسجل العميل
  if (!customer.payments) customer.payments = [];
  customer.payments.push(payment);

  // تحديث الديون والفواتير
  updateCustomerDebt(customer, amount, invoiceId);

  // حفظ التغييرات
  const customerIndex = customersList.findIndex(c => c.id === customerId);
  customersList[customerIndex] = customer;

  closePaymentModal();
  renderCustomers();
  showNotification(`تم تسديد ${formatPrice(amount)} بنجاح`, 'success');
}

// تحديث دين العميل
function updateCustomerDebt(customer, amount, invoiceId) {
  let remainingAmount = amount;

  if (invoiceId && invoiceId !== 'توزيع تلقائي') {
    // تسديد فاتورة محددة
    const invoice = customer.invoices.find(inv => inv.id === invoiceId);
    if (invoice && invoice.remaining > 0) {
      const paymentForInvoice = Math.min(remainingAmount, invoice.remaining);
      invoice.paid += paymentForInvoice;
      invoice.remaining -= paymentForInvoice;

      if (invoice.remaining === 0) {
        invoice.status = 'paid';
      } else if (invoice.paid > 0) {
        invoice.status = 'partial';
      }

      remainingAmount -= paymentForInvoice;
    }
  }

  // توزيع المبلغ المتبقي على الفواتير غير المدفوعة
  if (remainingAmount > 0) {
    const unpaidInvoices = customer.invoices.filter(inv => inv.remaining > 0);

    for (const invoice of unpaidInvoices) {
      if (remainingAmount <= 0) break;

      const paymentForInvoice = Math.min(remainingAmount, invoice.remaining);
      invoice.paid += paymentForInvoice;
      invoice.remaining -= paymentForInvoice;

      if (invoice.remaining === 0) {
        invoice.status = 'paid';
      } else if (invoice.paid > 0) {
        invoice.status = 'partial';
      }

      remainingAmount -= paymentForInvoice;
    }
  }

  // تحديث إجمالي الدين
  customer.totalDebt = customer.invoices.reduce((total, inv) => total + inv.remaining, 0);
}

// إغلاق النوافذ
function closeCustomerDetailsModal() {
  const modal = document.getElementById('customerDetailsModal');
  if (modal) modal.remove();
}

function closeInvoiceDetailsModal() {
  const modal = document.getElementById('invoiceDetailsModal');
  if (modal) modal.remove();
}

function closePaymentModal() {
  const modal = document.getElementById('paymentModal');
  if (modal) modal.remove();
}

// تصدير الوظائف للاستخدام العام
window.addNewCustomer = addNewCustomer;
window.editCustomer = editCustomer;
window.deleteCustomer = deleteCustomer;
window.closeSimpleCustomerModal = closeSimpleCustomerModal;
window.saveSimpleCustomer = saveSimpleCustomer;
window.searchCustomers = searchCustomers;
window.filterCustomers = filterCustomers;
window.openInvoiceDetails = openInvoiceDetails;
window.openPaymentModal = openPaymentModal;
window.processPayment = processPayment;
window.closeInvoiceDetailsModal = closeInvoiceDetailsModal;
window.closePaymentModal = closePaymentModal;
window.openCustomerRecord = openCustomerRecord;

// فتح صفحة سجل العميل
function openCustomerRecord(customerId) {
  window.location.href = `customer-record.html?id=${customerId}`;
}

// إضافة أنماط CSS لنافذة تفاصيل العميل
function addCustomerDetailsStyles() {
  if (document.getElementById('customerDetailsStyles')) return;

  const styles = `
    <style id="customerDetailsStyles">
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      }

      .modal-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #e9ecef;
      }

      .modal-header h3 {
        margin: 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }

      .modal-close {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .modal-close:hover {
        background: #f8f9fa;
        color: #495057;
      }

      .modal-body {
        padding: 24px;
      }

      .modal-footer {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        padding: 20px 24px;
        border-top: 1px solid #e9ecef;
      }

      .btn {
        padding: 10px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s ease;
      }

      .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #ddd;
      }

      .btn-outline:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
      }

      .btn-primary {
        background: #007bff;
        color: white;
        border: 1px solid #007bff;
      }

      .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
      }

      .btn-success {
        background: #28a745;
        color: white;
        border: 1px solid #28a745;
      }

      .btn-success:hover {
        background: #1e7e34;
        border-color: #1e7e34;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}

// إضافة أنماط CSS لنافذة تفاصيل الفاتورة
function addInvoiceDetailsStyles() {
  if (document.getElementById('invoiceDetailsStyles')) return;

  const styles = `
    <style id="invoiceDetailsStyles">
      .invoice-info {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .invoice-customer,
      .invoice-summary,
      .invoice-items,
      .invoice-notes {
        padding: 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
      }

      .invoice-customer h4,
      .invoice-summary h4,
      .invoice-items h4,
      .invoice-notes h4 {
        margin: 0 0 12px 0;
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
      }

      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid #e9ecef;
      }

      .summary-row:last-child {
        border-bottom: none;
      }

      .debt-amount {
        color: #dc3545;
        font-weight: 700;
      }

      .paid-amount {
        color: #28a745;
        font-weight: 700;
      }

      .status.paid {
        color: #28a745;
        font-weight: 600;
      }

      .status.partial {
        color: #ffc107;
        font-weight: 600;
      }

      .status.unpaid {
        color: #dc3545;
        font-weight: 600;
      }

      .items-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .item-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 8px;
        padding: 8px;
        background: white;
        border-radius: 4px;
        align-items: center;
      }

      .item-name {
        font-weight: 500;
        color: #2c3e50;
      }

      .item-qty {
        text-align: center;
        color: #6c757d;
      }

      .item-price,
      .item-total {
        text-align: left;
        font-weight: 600;
        color: #28a745;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}

// إضافة أنماط CSS لنافذة التسديد
function addPaymentModalStyles() {
  if (document.getElementById('paymentModalStyles')) return;

  const styles = `
    <style id="paymentModalStyles">
      .payment-info {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .customer-debt {
        padding: 16px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        border-radius: 8px;
      }

      .customer-debt h4 {
        margin: 0 0 8px 0;
        color: #856404;
      }

      .customer-debt p {
        margin: 0;
        color: #856404;
        font-weight: 600;
      }

      .payment-form {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .form-group label {
        font-weight: 500;
        color: #2c3e50;
        font-size: 14px;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        padding: 10px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s ease;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }

      .form-group textarea {
        resize: vertical;
        min-height: 80px;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}
