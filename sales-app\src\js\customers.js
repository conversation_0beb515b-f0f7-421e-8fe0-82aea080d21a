// ===== إدارة صفحة العملاء والموردين =====

// متغيرات عامة
let customersData = {
  totalCustomers: 0,
  vipCustomers: 0,
  averagePurchases: 0,
  inactiveCustomers: 0,
  customers: []
};

let database = null;

// ===== تهيئة صفحة العملاء =====
document.addEventListener('DOMContentLoaded', function() {
  initializeCustomersPage();
});

async function initializeCustomersPage() {
  try {
    console.log('🔄 بدء تهيئة صفحة العملاء...');

    // تهيئة قاعدة البيانات
    await initializeDatabase();

    // تحميل بيانات العملاء
    await loadCustomersData();

    // تحديث الإحصائيات
    updateCustomersStats();

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // تحميل جدول العملاء
    loadCustomersTable();

    console.log('✅ تم تهيئة صفحة العملاء بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة صفحة العملاء:', error);
    showNotification('حدث خطأ في تحميل بيانات العملاء', 'error');
  }
}

// تهيئة قاعدة البيانات
async function initializeDatabase() {
  try {
    if (window.electronAPI) {
      database = window.electronAPI;
      console.log('✅ تم الاتصال بقاعدة البيانات');
    } else {
      throw new Error('electronAPI غير متاح');
    }
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    throw error;
  }
}

// تحميل بيانات العملاء من قاعدة البيانات
async function loadCustomersData() {
  try {
    console.log('🔄 تحميل بيانات العملاء من قاعدة البيانات...');

    // التحقق من وجود قاعدة البيانات
    if (!database) {
      throw new Error('❌ قاعدة البيانات غير متصلة! يرجى إعادة تشغيل التطبيق.');
    }

    // التحقق من وجود وظيفة getAllCustomers
    if (typeof database.getAllCustomers !== 'function') {
      console.error('❌ وظائف قاعدة البيانات المتاحة:', Object.keys(database));
      throw new Error('❌ وظيفة getAllCustomers غير موجودة في قاعدة البيانات!');
    }

    // جلب جميع العملاء
    console.log('📡 محاولة جلب العملاء من قاعدة البيانات...');
    const customers = await database.getAllCustomers();
    console.log('📊 استجابة قاعدة البيانات:', customers);
    console.log('📊 نوع البيانات:', typeof customers);
    console.log(`📊 عدد العملاء: ${customers?.length || 0}`);

    if (!customers) {
      throw new Error('❌ قاعدة البيانات أرجعت null! تحقق من ملف قاعدة البيانات.');
    }

    if (!Array.isArray(customers)) {
      throw new Error(`❌ قاعدة البيانات أرجعت نوع بيانات خاطئ: ${typeof customers}. متوقع: Array`);
    }

    // تحويل البيانات للتنسيق المطلوب
    console.log('🔄 تحويل بيانات العملاء...');
    customersData.customers = customers.map((customer, index) => {
      console.log(`📝 معالجة العميل ${index + 1}:`, customer);

      if (!customer.id) {
        console.warn(`⚠️ العميل ${index + 1} لا يحتوي على معرف!`);
      }

      return {
        id: customer.id,
        name: customer.full_name || 'غير محدد',
        type: getCustomerTypeText(customer.type),
        phone: customer.phone || 'غير محدد',
        email: customer.email || '',
        city: customer.region || 'غير محدد',
        totalPurchases: customer.total_purchases || 0,
        invoicesCount: customer.total_purchases || 0,
        lastVisit: formatLastVisit(customer.updated_at),
        status: 'active',
        // بيانات إضافية
        idNumber: customer.id_number || 'غير محدد',
        region: customer.region || 'غير محدد',
        createdAt: customer.created_at
      };
    });

    // حساب الإحصائيات
    calculateCustomersStats();

    console.log('✅ تم تحميل بيانات العملاء بنجاح');
    console.log('📋 الإحصائيات:', customersData.stats);
    console.log('📋 قائمة العملاء النهائية:', customersData.customers);
  } catch (error) {
    console.error('❌ خطأ مفصل في تحميل بيانات العملاء:', {
      message: error.message,
      stack: error.stack,
      database: !!database,
      databaseType: typeof database,
      databaseMethods: database ? Object.keys(database) : 'لا توجد'
    });

    customersData.customers = [];
    showDetailedError('خطأ في تحميل بيانات العملاء', error.message);
    throw error;
  }
}

// حساب إحصائيات العملاء
function calculateCustomersStats() {
  const customers = customersData.customers;

  customersData.totalCustomers = customers.length;
  customersData.vipCustomers = customers.filter(c => c.type === 'عميل مميز').length;
  customersData.inactiveCustomers = customers.filter(c => c.status === 'inactive').length;

  // حساب متوسط المشتريات
  const totalPurchases = customers.reduce((sum, c) => sum + (c.totalPurchases || 0), 0);
  customersData.averagePurchases = customers.length > 0 ? Math.round(totalPurchases / customers.length) : 0;
}

// تحويل نوع العميل إلى نص
function getCustomerTypeText(type) {
  const types = {
    'individual': 'عميل فرد',
    'company': 'عميل تجاري',
    'vip': 'عميل مميز'
  };
  return types[type] || 'عميل عادي';
}

// تنسيق آخر زيارة
function formatLastVisit(lastVisit) {
  if (!lastVisit) return 'لم يزر بعد';

  const now = new Date();
  const visitDate = new Date(lastVisit);
  const diffInDays = Math.floor((now - visitDate) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return 'اليوم';
  if (diffInDays === 1) return 'أمس';
  if (diffInDays < 7) return `منذ ${diffInDays} أيام`;
  if (diffInDays < 30) return `منذ ${Math.floor(diffInDays / 7)} أسابيع`;
  return `منذ ${Math.floor(diffInDays / 30)} شهور`;
}

// ===== تحديث الإحصائيات =====
function updateCustomersStats() {
  updateStatCard('total-customers', customersData.totalCustomers);
  updateStatCard('vip-customers', customersData.vipCustomers);
  updateStatCard('average-purchases', customersData.averagePurchases, 'ريال');
  updateStatCard('inactive-customers', customersData.inactiveCustomers);
}

function updateStatCard(cardType, value, suffix = '') {
  const statValue = document.querySelector(`[data-stat="${cardType}"] .stat-value`);
  if (statValue) {
    if (suffix === 'ريال') {
      statValue.textContent = `${value.toLocaleString('ar-SA')} ${suffix}`;
    } else {
      statValue.textContent = value.toLocaleString('ar-SA');
    }
  }
}

// ===== إعداد مستمعي الأحداث =====
function setupEventListeners() {
  // أزرار التبويبات
  const tabButtons = document.querySelectorAll('.tab-btn');
  tabButtons.forEach(button => {
    button.addEventListener('click', handleTabSwitch);
  });

  // أزرار العرض
  const viewButtons = document.querySelectorAll('.view-btn');
  viewButtons.forEach(button => {
    button.addEventListener('click', switchView);
  });

  // مربع البحث
  const searchInput = document.querySelector('.search-input input');
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }

  // فلاتر التصفية
  const filterSelects = document.querySelectorAll('.customers-filters select');
  filterSelects.forEach(select => {
    select.addEventListener('change', applyFilters);
  });
}

// ===== تحميل جدول العملاء =====
function loadCustomersTable() {
  const tableBody = document.querySelector('.customers-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  customersData.customers.forEach(customer => {
    const row = createCustomerRow(customer);
    tableBody.appendChild(row);
  });
}

function createCustomerRow(customer) {
  const row = document.createElement('tr');
  row.innerHTML = `
    <td>
      <div class="customer-profile">
        <div class="customer-avatar">
          <span class="material-icons">person</span>
        </div>
        <div class="customer-details">
          <span class="customer-name">${customer.name}</span>
          <span class="customer-type">${customer.type}</span>
        </div>
      </div>
    </td>
    <td>
      <div class="contact-info">
        <span class="phone">${customer.phone}</span>
        <span class="email">${customer.email}</span>
      </div>
    </td>
    <td>${customer.city}</td>
    <td>
      <span class="total-purchases">${customer.totalPurchases.toLocaleString('ar-SA')} ريال</span>
      <span class="purchases-count">${customer.invoicesCount} فاتورة</span>
    </td>
    <td>${customer.lastVisit}</td>
    <td>
      <span class="status-badge ${customer.status}">${getStatusText(customer.status)}</span>
    </td>
    <td>
      <div class="action-buttons">
        <button class="btn-icon" title="عرض" onclick="viewCustomer('${customer.id}')">
          <span class="material-icons">visibility</span>
        </button>
        <button class="btn-icon" title="تعديل" onclick="editCustomer('${customer.id}')">
          <span class="material-icons">edit</span>
        </button>
        <button class="btn-icon" title="فواتير" onclick="viewCustomerInvoices('${customer.id}')">
          <span class="material-icons">receipt_long</span>
        </button>
        <button class="btn-icon" title="المزيد" onclick="showCustomerMenu('${customer.id}')">
          <span class="material-icons">more_vert</span>
        </button>
      </div>
    </td>
  `;

  return row;
}

function getStatusText(status) {
  const statusTexts = {
    active: 'نشط',
    inactive: 'غير نشط',
    blocked: 'محظور'
  };
  return statusTexts[status] || status;
}

// ===== إدارة التبويبات =====
function handleTabSwitch(event) {
  const tabType = event.currentTarget.getAttribute('data-tab');

  // إزالة الفئة النشطة من جميع التبويبات
  document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.classList.remove('active');
  });

  // إضافة الفئة النشطة للتبويب المحدد
  event.currentTarget.classList.add('active');

  switchTab(tabType);
}

function switchTab(tabType) {
  let filteredData = [];

  switch(tabType) {
    case 'customers':
      filteredData = customersData.customers.filter(c => c.type !== 'مورد');
      break;
    case 'suppliers':
      // سيتم إضافة بيانات الموردين لاحقاً
      filteredData = [];
      break;
    case 'vip':
      filteredData = customersData.customers.filter(c => c.type === 'عميل مميز');
      break;
    default:
      filteredData = customersData.customers;
  }

  updateCustomersTable(filteredData);
  showNotification(`تم التبديل إلى تبويب ${getTabText(tabType)}`, 'success');
}

function getTabText(tabType) {
  const tabTexts = {
    customers: 'العملاء',
    suppliers: 'الموردين',
    vip: 'العملاء المميزين'
  };
  return tabTexts[tabType] || tabType;
}

// ===== وظائف الفلترة والبحث =====
function applyFilters() {
  const cityFilter = document.querySelector('select[name="city"]')?.value;
  const typeFilter = document.querySelector('select[name="type"]')?.value;
  const statusFilter = document.querySelector('select[name="status"]')?.value;

  let filteredCustomers = [...customersData.customers];

  if (cityFilter && cityFilter !== 'جميع المناطق') {
    filteredCustomers = filteredCustomers.filter(customer => customer.city === cityFilter);
  }

  if (typeFilter && typeFilter !== 'جميع الأنواع') {
    filteredCustomers = filteredCustomers.filter(customer => customer.type === typeFilter);
  }

  if (statusFilter && statusFilter !== 'جميع الحالات') {
    filteredCustomers = filteredCustomers.filter(customer => {
      return getStatusText(customer.status) === statusFilter;
    });
  }

  updateCustomersTable(filteredCustomers);
  showNotification('تم تطبيق الفلتر بنجاح', 'success');
}

function handleSearch(event) {
  const searchTerm = event.target.value.toLowerCase();

  const filteredCustomers = customersData.customers.filter(customer => {
    return customer.name.toLowerCase().includes(searchTerm) ||
           customer.phone.includes(searchTerm) ||
           customer.email.toLowerCase().includes(searchTerm);
  });

  updateCustomersTable(filteredCustomers);
}

function updateCustomersTable(customers) {
  const tableBody = document.querySelector('.customers-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  customers.forEach(customer => {
    const row = createCustomerRow(customer);
    tableBody.appendChild(row);
  });

  // تحديث عدد النتائج
  updateResultsCount(customers.length);
}

function updateResultsCount(count) {
  const paginationInfo = document.querySelector('.pagination-info');
  if (paginationInfo) {
    paginationInfo.textContent = `عرض ${count} من ${customersData.customers.length}`;
  }
}

// ===== تبديل طريقة العرض =====
function switchView(event) {
  const viewType = event.currentTarget.getAttribute('data-view');

  // إزالة الفئة النشطة من جميع الأزرار
  document.querySelectorAll('.view-btn').forEach(btn => {
    btn.classList.remove('active');
  });

  // إضافة الفئة النشطة للزر المحدد
  event.currentTarget.classList.add('active');

  if (viewType === 'cards') {
    showCardsView();
  } else {
    showTableView();
  }

  showNotification(`تم التبديل إلى عرض ${viewType === 'cards' ? 'البطاقات' : 'الجدول'}`, 'info');
}

function showTableView() {
  const tableContainer = document.querySelector('.customers-table-container');
  if (tableContainer) {
    tableContainer.style.display = 'block';
  }
}

function showCardsView() {
  // سيتم تنفيذها لاحقاً
  showNotification('عرض البطاقات قيد التطوير', 'info');
}

// ===== وظائف الإجراءات =====
function addNewCustomer() {
  console.log('🔄 محاولة فتح نافذة إضافة العميل...');

  // التحقق من وجود النافذة
  if (window.customerModal && typeof window.customerModal.show === 'function') {
    console.log('✅ تم العثور على نافذة العملاء، جاري فتحها...');
    window.customerModal.show();
  } else {
    console.log('⚠️ نافذة العملاء غير متاحة، إنشاء نافذة بديلة...');
    createSimpleCustomerModal();
  }
}

// إنشاء نافذة بسيطة للعملاء
function createSimpleCustomerModal() {
  // إزالة أي نافذة موجودة
  const existingModal = document.getElementById('simpleCustomerModal');
  if (existingModal) {
    existingModal.remove();
  }

  // إنشاء النافذة
  const modalHTML = `
    <div id="simpleCustomerModal" class="modal-overlay show">
      <div class="modal-container">
        <div class="modal-header">
          <h3>إضافة عميل جديد</h3>
          <button class="modal-close" onclick="closeSimpleCustomerModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="modal-body">
          <form id="simpleCustomerForm">
            <div class="form-group">
              <label class="form-label">الاسم الثلاثي *</label>
              <input type="text" name="fullName" class="form-control" placeholder="الاسم الأول الأوسط الأخير" required>
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهوية الوطنية العراقية *</label>
              <input type="text" name="idNumber" class="form-control" placeholder="رقم الهوية الوطنية العراقية (12 رقم)" required maxlength="12">
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهاتف *</label>
              <input type="tel" name="phone" class="form-control" placeholder="07xxxxxxxxx" required>
            </div>

            <div class="form-group">
              <label class="form-label">البريد الإلكتروني</label>
              <input type="email" name="email" class="form-control" placeholder="البريد الإلكتروني (اختياري)">
            </div>

            <div class="form-group">
              <label class="form-label">المحافظة *</label>
              <select name="region" class="form-control" required>
                <option value="">اختر المحافظة</option>
                <option value="بغداد">بغداد</option>
                <option value="البصرة">البصرة</option>
                <option value="نينوى">نينوى</option>
                <option value="أربيل">أربيل</option>
                <option value="النجف">النجف</option>
                <option value="كربلاء">كربلاء</option>
                <option value="بابل">بابل</option>
                <option value="ديالى">ديالى</option>
                <option value="الأنبار">الأنبار</option>
                <option value="كركوك">كركوك</option>
                <option value="صلاح الدين">صلاح الدين</option>
                <option value="واسط">واسط</option>
                <option value="ذي قار">ذي قار</option>
                <option value="المثنى">المثنى</option>
                <option value="القادسية">القادسية</option>
                <option value="ميسان">ميسان</option>
                <option value="دهوك">دهوك</option>
                <option value="السليمانية">السليمانية</option>
                <option value="حلبجة">حلبجة</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">نوع العميل</label>
              <select name="type" class="form-control">
                <option value="individual">فرد</option>
                <option value="company">شركة</option>
              </select>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="closeSimpleCustomerModal()">
            إلغاء
          </button>
          <button type="button" class="btn btn-primary" onclick="saveSimpleCustomer()">
            <span class="material-icons">save</span>
            حفظ العميل
          </button>
        </div>
      </div>
    </div>
  `;

  // إضافة النافذة للصفحة
  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // إضافة الأنماط
  addCustomerModalStyles();

  console.log('✅ تم إنشاء نافذة العميل البديلة');
}

async function editCustomer(customerId) {
  try {
    console.log('🔄 تحميل بيانات العميل للتعديل:', customerId);

    // جلب بيانات العميل من قاعدة البيانات
    const customer = await database.getCustomerById(customerId);
    if (!customer) {
      showNotification('لم يتم العثور على العميل', 'error');
      return;
    }

    // فتح نافذة التعديل
    createSimpleCustomerModal();

    // ملء البيانات بعد تحميل النافذة
    setTimeout(() => {
      const form = document.getElementById('simpleCustomerForm');
      if (form) {
        form.fullName.value = customer.name;
        form.idNumber.value = customer.id_number;
        form.phone.value = customer.phone;
        form.region.value = customer.region;
        form.type.value = customer.type;

        // إضافة حقل البريد الإلكتروني إذا لم يكن موجوداً
        let emailField = form.email;
        if (!emailField) {
          const emailGroup = document.createElement('div');
          emailGroup.className = 'form-group';
          emailGroup.innerHTML = `
            <label class="form-label">البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control" placeholder="البريد الإلكتروني (اختياري)">
          `;
          form.appendChild(emailGroup);
          emailField = form.email;
        }
        if (emailField) emailField.value = customer.email || '';

        // تغيير النص والوظيفة
        const title = document.querySelector('#simpleCustomerModal .modal-header h3');
        const saveBtn = document.querySelector('#simpleCustomerModal .btn-primary');

        if (title) title.textContent = 'تعديل بيانات العميل';
        if (saveBtn) {
          saveBtn.onclick = () => updateCustomer(customerId);
        }
      }
    }, 100);

  } catch (error) {
    console.error('❌ خطأ في تحميل بيانات العميل:', error);
    showNotification('حدث خطأ في تحميل بيانات العميل', 'error');
  }
}

async function deleteCustomer(customerId) {
  try {
    console.log('🔄 محاولة حذف العميل:', customerId);

    // جلب بيانات العميل
    const customer = await database.getCustomerById(customerId);
    if (!customer) {
      showNotification('لم يتم العثور على العميل', 'error');
      return;
    }

    // التحقق من وجود فواتير للعميل
    const customerInvoices = await database.getCustomerInvoices(customerId);
    if (customerInvoices && customerInvoices.length > 0) {
      showNotification('لا يمكن حذف العميل - يوجد فواتير مرتبطة به', 'error');
      return;
    }

    // تأكيد الحذف
    if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
      await database.deleteCustomer(customerId);
      showNotification('تم حذف العميل بنجاح', 'success');

      // إعادة تحميل البيانات
      await loadCustomersData();
      updateCustomersStats();
      loadCustomersTable();
    }

  } catch (error) {
    console.error('❌ خطأ في حذف العميل:', error);
    showNotification('حدث خطأ في حذف العميل: ' + error.message, 'error');
  }
}

// تحديث بيانات العميل
async function updateCustomer(customerId) {
  const form = document.getElementById('simpleCustomerForm');
  if (!form) return;

  const formData = new FormData(form);
  const updatedData = {
    name: formData.get('fullName'),
    id_number: formData.get('idNumber'),
    phone: formData.get('phone'),
    region: formData.get('region'),
    type: formData.get('type'),
    email: formData.get('email') || ''
  };

  // التحقق من البيانات
  if (!updatedData.name || !updatedData.id_number || !updatedData.phone || !updatedData.region) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // التحقق من صحة رقم الهاتف العراقي
  const phoneRegex = /^07\d{9}$/;
  if (!phoneRegex.test(updatedData.phone)) {
    showNotification('يرجى إدخال رقم هاتف عراقي صحيح (07xxxxxxxxx)', 'error');
    return;
  }

  // التحقق من صحة رقم الهوية العراقية
  const idRegex = /^\d{12}$/;
  if (!idRegex.test(updatedData.id_number)) {
    showNotification('يرجى إدخال رقم هوية وطنية عراقية صحيح (12 رقم)', 'error');
    return;
  }

  try {
    console.log('🔄 تحديث بيانات العميل:', customerId, updatedData);

    // التحقق من عدم تكرار رقم الهوية مع عميل آخر
    const existingCustomer = await database.getCustomerByIdNumber(updatedData.id_number);
    if (existingCustomer && existingCustomer.id !== customerId) {
      showNotification('رقم الهوية موجود مع عميل آخر', 'error');
      return;
    }

    // تحديث العميل في قاعدة البيانات
    await database.updateCustomer(customerId, updatedData);
    console.log('✅ تم تحديث العميل بنجاح');

    closeSimpleCustomerModal();
    showNotification('تم تحديث بيانات العميل بنجاح', 'success');

    // إعادة تحميل البيانات
    await loadCustomersData();
    updateCustomersStats();
    loadCustomersTable();

  } catch (error) {
    console.error('❌ خطأ في تحديث العميل:', error);
    showNotification('حدث خطأ في تحديث العميل: ' + error.message, 'error');
  }
}

// إغلاق النافذة البسيطة
function closeSimpleCustomerModal() {
  const modal = document.getElementById('simpleCustomerModal');
  if (modal) {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.remove();
    }, 300);
  }
}

// حفظ العميل من النافذة البسيطة
async function saveSimpleCustomer() {
  const form = document.getElementById('simpleCustomerForm');
  if (!form) return;

  // جمع البيانات
  const formData = new FormData(form);
  const customerData = {
    name: formData.get('fullName'),
    id_number: formData.get('idNumber'),
    phone: formData.get('phone'),
    region: formData.get('region'),
    type: formData.get('type'),
    email: formData.get('email') || '',
    status: 'active'
  };

  // التحقق من البيانات المطلوبة
  if (!customerData.name || !customerData.id_number || !customerData.phone || !customerData.region) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // التحقق من صحة رقم الهاتف العراقي
  const phoneRegex = /^07\d{9}$/;
  if (!phoneRegex.test(customerData.phone)) {
    showNotification('يرجى إدخال رقم هاتف عراقي صحيح (07xxxxxxxxx)', 'error');
    return;
  }

  // التحقق من صحة رقم الهوية العراقية
  const idRegex = /^\d{12}$/;
  if (!idRegex.test(customerData.id_number)) {
    showNotification('يرجى إدخال رقم هوية وطنية عراقية صحيح (12 رقم)', 'error');
    return;
  }

  try {
    console.log('🔄 حفظ العميل في قاعدة البيانات...', customerData);

    // التحقق من عدم وجود العميل مسبقاً
    const existingCustomer = await database.getCustomerByIdNumber(customerData.id_number);
    if (existingCustomer) {
      showNotification('رقم الهوية موجود مسبقاً', 'error');
      return;
    }

    // إضافة العميل إلى قاعدة البيانات
    const customerId = await database.addCustomer(customerData);
    console.log('✅ تم حفظ العميل برقم:', customerId);

    showNotification('تم إضافة العميل بنجاح', 'success');
    closeSimpleCustomerModal();

    // إعادة تحميل بيانات العملاء
    await loadCustomersData();
    updateCustomersStats();
    loadCustomersTable();

  } catch (error) {
    console.error('❌ خطأ في حفظ العميل:', error);
    showNotification('حدث خطأ في حفظ العميل: ' + error.message, 'error');
  }
}

// إضافة أنماط النافذة
function addCustomerModalStyles() {
  // التحقق من وجود الأنماط
  if (document.getElementById('customerModalStyles')) return;

  const styles = document.createElement('style');
  styles.id = 'customerModalStyles';
  styles.textContent = `
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .modal-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .modal-container {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      transform: translateY(-20px);
      transition: transform 0.3s ease;
    }

    .modal-overlay.show .modal-container {
      transform: translateY(0);
    }

    .modal-header {
      padding: 20px 24px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .modal-header h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .modal-close {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 50%;
      cursor: pointer;
      color: #666;
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: #f5f5f5;
      color: #333;
    }

    .modal-body {
      padding: 24px;
    }

    .modal-footer {
      padding: 20px 24px;
      border-top: 1px solid #eee;
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-label {
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .form-control {
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-outline {
      background: transparent;
      color: #666;
      border: 1px solid #ddd;
    }

    .btn-outline:hover {
      background: #f8f9fa;
      border-color: #adb5bd;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }

      .modal-container {
        width: 95%;
        margin: 20px;
      }
    }
  `;

  document.head.appendChild(styles);
}

// ===== وظائف مساعدة =====
function showNotification(message, type = 'info') {
  // إنشاء إشعار بسيط
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'}-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-weight: 500;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// ===== تصدير الوظائف =====
window.CustomersPage = {
  addNewCustomer,
  editCustomer,
  deleteCustomer,
  switchTab
};

// وظائف مساعدة للعرض
function renderCustomersGrid() {
  const grid = document.getElementById('customersGrid');
  if (!grid) return;

  const customers = customersData.customers;

  if (customers.length === 0) {
    grid.innerHTML = `
      <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #7f8c8d;">
        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px;">people_outline</span>
        <p>لا توجد عملاء حالياً</p>
        <button class="btn btn-primary" onclick="addNewCustomer()">إضافة أول عميل</button>
      </div>
    `;
    return;
  }

  grid.innerHTML = customers.map(customer => `
    <div class="customer-card" onclick="openCustomerRecord('${customer.id}')">
      <div class="customer-header">
        <div class="customer-avatar">
          <span class="material-icons">${customer.type === 'عميل تجاري' ? 'business' : 'person'}</span>
        </div>
        <div class="customer-info">
          <h3>${customer.name}</h3>
          <span class="customer-type">${customer.type}</span>
        </div>
      </div>

      <div class="customer-details">
        <div class="customer-detail">
          <span class="material-icons">phone</span>
          <span>${customer.phone}</span>
        </div>
        <div class="customer-detail">
          <span class="material-icons">location_on</span>
          <span>${customer.city}</span>
        </div>
        <div class="customer-detail">
          <span class="material-icons">shopping_cart</span>
          <span>${customer.totalPurchases || 0} د.ع</span>
        </div>
        <div class="customer-detail">
          <span class="material-icons">receipt</span>
          <span>${customer.invoicesCount || 0} فاتورة</span>
        </div>
      </div>

      <div class="customer-actions" onclick="event.stopPropagation()">
        <button class="btn-icon edit-btn" title="تعديل" onclick="editCustomer('${customer.id}')">
          <span class="material-icons">edit</span>
        </button>
        <button class="btn-icon delete-btn" title="حذف" onclick="deleteCustomer('${customer.id}')">
          <span class="material-icons">delete</span>
        </button>
        <button class="btn-icon view-btn" title="عرض السجل" onclick="openCustomerRecord('${customer.id}')">
          <span class="material-icons">visibility</span>
        </button>
      </div>
    </div>
  `).join('');
}

// البحث في العملاء
function searchCustomers() {
  const searchInput = document.getElementById('customerSearch');
  if (!searchInput) return;

  const searchTerm = searchInput.value.toLowerCase();
  const filtered = customersData.customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm) ||
    customer.phone.includes(searchTerm) ||
    customer.idNumber.includes(searchTerm) ||
    customer.city.toLowerCase().includes(searchTerm)
  );

  updateCustomersTable(filtered);
}

// فلترة العملاء
function filterCustomers() {
  const filterSelect = document.getElementById('customerFilter');
  if (!filterSelect) return;

  const filterValue = filterSelect.value;
  let filtered = customersData.customers;

  if (filterValue !== 'all') {
    filtered = customersData.customers.filter(customer => customer.type === filterValue);
  }

  updateCustomersTable(filtered);
}

// تحديث عدد العملاء
function updateCustomersCount() {
  const countElement = document.getElementById('customersCount');
  if (countElement) {
    countElement.textContent = `${customersData.totalCustomers} عميل`;
  }
}

// تنسيق السعر
function formatPrice(price) {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price) + ' د.ع';
}

// فتح صفحة سجل العميل
function openCustomerRecord(customerId) {
  window.location.href = `customer-record.html?id=${customerId}`;
}

// تصدير الوظائف للاستخدام العام
window.addNewCustomer = addNewCustomer;
window.editCustomer = editCustomer;
window.deleteCustomer = deleteCustomer;
window.closeSimpleCustomerModal = closeSimpleCustomerModal;
window.saveSimpleCustomer = saveSimpleCustomer;
window.updateCustomer = updateCustomer;
window.searchCustomers = searchCustomers;
window.filterCustomers = filterCustomers;
window.openCustomerRecord = openCustomerRecord;
window.renderCustomersGrid = renderCustomersGrid;



// فتح صفحة سجل العميل
function openCustomerRecord(customerId) {
  window.location.href = `customer-record.html?id=${customerId}`;
}

// عرض رسالة خطأ مفصلة
function showDetailedError(title, details) {
  console.error(`🚨 ${title}:`, details);

  // إنشاء نافذة خطأ مفصلة
  const errorModal = document.createElement('div');
  errorModal.className = 'error-modal';
  errorModal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
  `;

  errorModal.innerHTML = `
    <div class="error-modal-content" style="
      background: white;
      border-radius: 12px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    ">
      <div class="error-header" style="
        padding: 20px 24px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 12px;
        background: #f8f9fa;
        border-radius: 12px 12px 0 0;
      ">
        <span class="material-icons" style="color: #dc3545; font-size: 24px;">error_outline</span>
        <h3 style="margin: 0; color: #dc3545; font-size: 18px;">${title}</h3>
        <button class="close-error" onclick="this.closest('.error-modal').remove()" style="
          margin-left: auto;
          background: none;
          border: none;
          padding: 8px;
          border-radius: 50%;
          cursor: pointer;
          color: #666;
        ">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="error-body" style="padding: 24px;">
        <p style="margin: 0 0 16px 0;"><strong>تفاصيل الخطأ:</strong></p>
        <div class="error-details" style="
          background: #f8f9fa;
          padding: 12px;
          border-radius: 6px;
          font-family: monospace;
          font-size: 14px;
          color: #dc3545;
          margin-bottom: 20px;
          word-break: break-word;
        ">${details}</div>
        <p style="margin: 0 0 12px 0;"><strong>الحلول المقترحة:</strong></p>
        <ul style="margin: 0; padding-right: 20px;">
          <li>تأكد من أن قاعدة البيانات متصلة</li>
          <li>أعد تشغيل التطبيق</li>
          <li>تحقق من ملف قاعدة البيانات</li>
          <li>تحقق من اتصال الإنترنت إذا كان مطلوباً</li>
        </ul>
      </div>
      <div class="error-footer" style="
        padding: 20px 24px;
        border-top: 1px solid #eee;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
      ">
        <button class="btn btn-primary" onclick="location.reload()" style="
          padding: 10px 16px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
        ">إعادة تحميل الصفحة</button>
        <button class="btn btn-secondary" onclick="this.closest('.error-modal').remove()" style="
          padding: 10px 16px;
          background: #6c757d;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
        ">إغلاق</button>
      </div>
    </div>
  `;

  document.body.appendChild(errorModal);
}