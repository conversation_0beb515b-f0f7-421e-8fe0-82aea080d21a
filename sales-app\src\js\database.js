// ===== قاعدة البيانات Better-SQLite3 =====

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class SalesDatabase {
  constructor() {
    this.db = null;
    this.dbPath = path.join(__dirname, '..', 'data', 'sales.db');
    this.init();
  }

  // تهيئة قاعدة البيانات
  init() {
    try {
      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // فتح/إنشاء قاعدة البيانات
      this.db = new Database(this.dbPath);

      // إعدادات الأداء
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000000');
      this.db.pragma('temp_store = memory');

      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

      // إنشاء الجداول
      this.createTables();

      // إدراج البيانات التجريبية
      this.insertSampleData();

    } catch (error) {
      console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
      throw error;
    }
  }

  // إنشاء الجداول
  createTables() {
    try {
      // جدول العملاء
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          full_name TEXT NOT NULL,
          id_number TEXT UNIQUE NOT NULL,
          phone TEXT NOT NULL,
          region TEXT NOT NULL,
          type TEXT DEFAULT 'individual',
          email TEXT DEFAULT '',
          total_debt REAL DEFAULT 0,
          total_purchases INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // جدول المنتجات
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          price REAL NOT NULL,
          category TEXT NOT NULL,
          icon TEXT DEFAULT 'inventory_2',
          stock INTEGER DEFAULT 0,
          unit TEXT DEFAULT 'قطعة',
          barcode TEXT UNIQUE,
          purchase_price REAL DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // جدول الفواتير
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS invoices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_number TEXT UNIQUE NOT NULL,
          customer_id INTEGER,
          customer_name TEXT NOT NULL,
          total REAL NOT NULL,
          paid_amount REAL DEFAULT 0,
          remaining_amount REAL DEFAULT 0,
          status TEXT DEFAULT 'pending',
          payment_method TEXT NOT NULL,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
      `);

      // جدول عناصر الفاتورة
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS invoice_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          product_id INTEGER,
          product_name TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          price REAL NOT NULL,
          total REAL NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES invoices (id),
          FOREIGN KEY (product_id) REFERENCES products (id)
        )
      `);

      // جدول المدفوعات
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          payment_method TEXT NOT NULL,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (invoice_id) REFERENCES invoices (id),
          FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
      `);

      // جدول الديون
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS debts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          invoice_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          remaining_amount REAL NOT NULL,
          status TEXT DEFAULT 'pending',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers (id),
          FOREIGN KEY (invoice_id) REFERENCES invoices (id)
        )
      `);

      // إنشاء الفهارس لتحسين الأداء
      this.createIndexes();

      // تحديث الجداول الموجودة
      this.updateExistingTables();

      console.log('✅ تم إنشاء جميع الجداول بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إنشاء الجداول:', error);
      throw error;
    }
  }

  // إنشاء الفهارس
  createIndexes() {
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(full_name);
      CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
      CREATE INDEX IF NOT EXISTS idx_customers_id_number ON customers(id_number);
      CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
      CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode);
      CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
      CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number);
      CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id);
      CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(created_at);
      CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON invoice_items(invoice_id);
      CREATE INDEX IF NOT EXISTS idx_invoice_items_product ON invoice_items(product_id);
    `);
  }

  // تحديث الجداول الموجودة لإضافة أعمدة جديدة
  updateExistingTables() {
    try {
      // التحقق من وجود عمود type في جدول العملاء
      const tableInfo = this.db.prepare("PRAGMA table_info(customers)").all();
      const hasTypeColumn = tableInfo.some(col => col.name === 'type');
      const hasEmailColumn = tableInfo.some(col => col.name === 'email');

      if (!hasTypeColumn) {
        console.log('🔄 إضافة عمود type لجدول العملاء...');
        this.db.exec("ALTER TABLE customers ADD COLUMN type TEXT DEFAULT 'individual'");
      }

      if (!hasEmailColumn) {
        console.log('🔄 إضافة عمود email لجدول العملاء...');
        this.db.exec("ALTER TABLE customers ADD COLUMN email TEXT DEFAULT ''");
      }

      console.log('✅ تم تحديث الجداول بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تحديث الجداول:', error);
    }
  }

  // إدراج البيانات التجريبية
  insertSampleData() {
    try {
      // التحقق من وجود بيانات
      const customerCount = this.db.prepare('SELECT COUNT(*) as count FROM customers').get();

      if (customerCount.count === 0) {
        console.log('📝 إدراج البيانات التجريبية...');

        // إدراج العملاء التجريبيين
        this.insertSampleCustomers();

        // إدراج المنتجات التجريبية
        this.insertSampleProducts();

        console.log('✅ تم إدراج البيانات التجريبية بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
    }
  }

  // إدراج عملاء تجريبيين
  insertSampleCustomers() {
    const insertCustomer = this.db.prepare(`
      INSERT INTO customers (full_name, id_number, phone, region)
      VALUES (?, ?, ?, ?)
    `);

    const customers = [
      ['محمد أحمد حسين', '123456789012', '0501234567', 'الرياض'],
      ['علي عبدالله كريم', '987654321098', '0509876543', 'جدة'],
      ['فاطمة حسن محمد', '456789123456', '0512345678', 'الدمام'],
      ['أحمد علي حسين', '789123456789', '0507654321', 'مكة'],
      ['زينب محمد علي', '321654987321', '0515432109', 'المدينة']
    ];

    const insertMany = this.db.transaction((customers) => {
      for (const customer of customers) {
        insertCustomer.run(...customer);
      }
    });

    insertMany(customers);
  }

  // إدراج منتجات تجريبية
  insertSampleProducts() {
    const insertProduct = this.db.prepare(`
      INSERT INTO products (name, price, category, icon, stock, unit, barcode, purchase_price)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const products = [
      ['مفك كهربائي', 25000, 'أدوات', 'build', 50, 'قطعة', '1234567890123', 20000],
      ['مثقاب كهربائي', 85000, 'أدوات', 'construction', 25, 'قطعة', '2345678901234', 70000],
      ['كابل كهربائي 2.5مم', 15000, 'كهربائيات', 'cable', 100, 'متر', '3456789012345', 12000],
      ['مفتاح كهربائي', 8000, 'كهربائيات', 'toggle_on', 200, 'قطعة', '4567890123456', 6000],
      ['أسمنت أبيض', 12000, 'مواد بناء', 'foundation', 80, 'كيس', '5678901234567', 10000],
      ['طوب أحمر', 500, 'مواد بناء', 'view_module', 1000, 'قطعة', '6789012345678', 400],
      ['مصباح LED 20 واط', 18000, 'كهربائيات', 'lightbulb', 75, 'قطعة', '7890123456789', 15000],
      ['منشار يدوي', 35000, 'أدوات', 'carpenter', 30, 'قطعة', '8901234567890', 28000],
      ['مسامير حديد', 2000, 'أدوات', 'hardware', 500, 'علبة', '9012345678901', 1500],
      ['دهان أبيض', 22000, 'مواد بناء', 'format_paint', 60, 'علبة', '0123456789012', 18000]
    ];

    const insertMany = this.db.transaction((products) => {
      for (const product of products) {
        insertProduct.run(...product);
      }
    });

    insertMany(products);
  }

  // ===== وظائف العملاء =====

  // الحصول على جميع العملاء
  getAllCustomers() {
    try {
      return this.db.prepare('SELECT * FROM customers ORDER BY full_name').all();
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error);
      return [];
    }
  }

  // الحصول على عميل بالمعرف
  getCustomerById(id) {
    try {
      return this.db.prepare('SELECT * FROM customers WHERE id = ?').get(id);
    } catch (error) {
      console.error('خطأ في جلب العميل:', error);
      return null;
    }
  }

  // الحصول على عميل برقم الهوية
  getCustomerByIdNumber(idNumber) {
    try {
      return this.db.prepare('SELECT * FROM customers WHERE id_number = ?').get(idNumber);
    } catch (error) {
      console.error('خطأ في البحث عن العميل برقم الهوية:', error);
      return null;
    }
  }

  // إضافة عميل جديد
  addCustomer(customerData) {
    try {
      const { name, id_number, phone, region, type, email } = customerData;
      const stmt = this.db.prepare(`
        INSERT INTO customers (full_name, id_number, phone, region, type, email)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(name, id_number, phone, region, type || 'individual', email || '');
      return result.lastInsertRowid;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  }

  // تحديث بيانات العميل
  updateCustomer(id, customerData) {
    try {
      const { name, id_number, phone, region, type, email } = customerData;
      const stmt = this.db.prepare(`
        UPDATE customers
        SET full_name = ?, id_number = ?, phone = ?, region = ?, type = ?, email = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      return stmt.run(name, id_number, phone, region, type || 'individual', email || '', id);
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  }

  // حذف عميل
  deleteCustomer(id) {
    try {
      // التحقق من عدم وجود ديون
      const customer = this.db.prepare('SELECT total_debt FROM customers WHERE id = ?').get(id);

      if (customer && customer.total_debt > 0) {
        throw new Error('لا يمكن حذف العميل لوجود ديون مستحقة');
      }

      const stmt = this.db.prepare('DELETE FROM customers WHERE id = ?');
      return stmt.run(id);
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  }

  // ===== وظائف المنتجات =====

  // الحصول على جميع المنتجات
  getAllProducts() {
    try {
      return this.db.prepare('SELECT * FROM products ORDER BY name').all();
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      return [];
    }
  }

  // إضافة منتج جديد
  addProduct(productData) {
    try {
      const { name, price, category, icon, stock, unit, barcode, purchasePrice } = productData;
      const stmt = this.db.prepare(`
        INSERT INTO products (name, price, category, icon, stock, unit, barcode, purchase_price)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      return stmt.run(name, price, category, icon, stock, unit, barcode, purchasePrice);
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  }

  // تحديث المخزون
  updateStock(productId, quantity) {
    try {
      const stmt = this.db.prepare(`
        UPDATE products
        SET stock = stock - ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      return stmt.run(quantity, productId);
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
      throw error;
    }
  }

  // ===== وظائف الفواتير =====

  // حفظ فاتورة جديدة
  saveInvoice(invoiceData) {
    const saveInvoiceTransaction = this.db.transaction((data) => {
      try {
        const { invoiceNumber, customerId, customerName, total, paidAmount, remainingAmount, status, paymentMethod, notes, items } = data;

        // 1. إدراج الفاتورة
        const insertInvoice = this.db.prepare(`
          INSERT INTO invoices (invoice_number, customer_id, customer_name, total, paid_amount, remaining_amount, status, payment_method, notes)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const invoiceResult = insertInvoice.run(invoiceNumber, customerId, customerName, total, paidAmount, remainingAmount, status, paymentMethod, notes);
        const invoiceId = invoiceResult.lastInsertRowid;

        // 2. إدراج عناصر الفاتورة
        const insertItem = this.db.prepare(`
          INSERT INTO invoice_items (invoice_id, product_id, product_name, quantity, price, total)
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        for (const item of items) {
          insertItem.run(invoiceId, item.productId, item.name, item.quantity, item.price, item.total);

          // 3. تحديث المخزون
          const updateStock = this.db.prepare('UPDATE products SET stock = stock - ? WHERE id = ?');
          updateStock.run(item.quantity, item.productId);
        }

        // 4. إضافة دين إذا كان هناك مبلغ متبقي
        if (remainingAmount > 0) {
          const insertDebt = this.db.prepare(`
            INSERT INTO debts (customer_id, invoice_id, amount, remaining_amount)
            VALUES (?, ?, ?, ?)
          `);
          insertDebt.run(customerId, invoiceId, remainingAmount, remainingAmount);

          // تحديث دين العميل
          const updateCustomerDebt = this.db.prepare('UPDATE customers SET total_debt = total_debt + ? WHERE id = ?');
          updateCustomerDebt.run(remainingAmount, customerId);
        }

        // 5. تحديث عدد المشتريات
        const updatePurchases = this.db.prepare('UPDATE customers SET total_purchases = total_purchases + 1 WHERE id = ?');
        updatePurchases.run(customerId);

        return invoiceId;
      } catch (error) {
        console.error('خطأ في حفظ الفاتورة:', error);
        throw error;
      }
    });

    return saveInvoiceTransaction(invoiceData);
  }

  // الحصول على فواتير العميل
  getCustomerInvoices(customerId) {
    try {
      return this.db.prepare(`
        SELECT i.*, GROUP_CONCAT(ii.product_name || ' x' || ii.quantity) as items_summary
        FROM invoices i
        LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
        WHERE i.customer_id = ?
        GROUP BY i.id
        ORDER BY i.created_at DESC
      `).all(customerId);
    } catch (error) {
      console.error('خطأ في جلب فواتير العميل:', error);
      return [];
    }
  }

  // ===== وظائف الإحصائيات =====

  // إحصائيات المبيعات اليومية
  getDailyStats() {
    try {
      return this.db.prepare(`
        SELECT
          COUNT(*) as total_invoices,
          COALESCE(SUM(total), 0) as total_sales,
          COALESCE(AVG(total), 0) as average_sale
        FROM invoices
        WHERE DATE(created_at) = DATE('now')
      `).get();
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات اليومية:', error);
      return { total_invoices: 0, total_sales: 0, average_sale: 0 };
    }
  }

  // المنتجات منخفضة المخزون
  getLowStockProducts(threshold = 10) {
    try {
      return this.db.prepare(`
        SELECT * FROM products
        WHERE stock <= ?
        ORDER BY stock ASC
      `).all(threshold);
    } catch (error) {
      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
      return [];
    }
  }

  // إغلاق قاعدة البيانات
  close() {
    try {
      if (this.db) {
        this.db.close();
        console.log('✅ تم إغلاق قاعدة البيانات');
      }
    } catch (error) {
      console.error('❌ خطأ في إغلاق قاعدة البيانات:', error);
    }
  }
}

// تصدير قاعدة البيانات
module.exports = SalesDatabase;
