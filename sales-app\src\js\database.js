// ===== إدارة قاعدة البيانات الوهمية =====

const path = require('path');
const fs = require('fs');

class SalesDatabase {
  constructor() {
    this.db = null; // سيكون null دائماً
    this.data = {
      categories: [],
      products: [],
      customers: [],
      invoices: [],
      users: []
    };
    this.init();
  }

  // ===== تهيئة قاعدة البيانات =====
  init() {
    try {
      console.log('✅ تم تهيئة قاعدة البيانات الوهمية بنجاح');
      this.insertDefaultData();
    } catch (error) {
      console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    }
  }

  // ===== إنشاء الجداول =====
  createTables() {
    // جدول الشركة
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS company (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        commercial_register TEXT,
        tax_number TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        website TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الفئات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        icon TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المنتجات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT UNIQUE,
        category_id INTEGER,
        description TEXT,
        purchase_price REAL DEFAULT 0,
        sale_price REAL DEFAULT 0,
        stock_quantity INTEGER DEFAULT 0,
        min_stock INTEGER DEFAULT 0,
        max_stock INTEGER DEFAULT 0,
        unit TEXT DEFAULT 'قطعة',
        barcode TEXT,
        image_path TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    `);

    // جدول العملاء
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        city TEXT,
        customer_type TEXT DEFAULT 'عادي',
        tax_number TEXT,
        credit_limit REAL DEFAULT 0,
        current_balance REAL DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الموردين
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        city TEXT,
        tax_number TEXT,
        contact_person TEXT,
        payment_terms TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الفواتير
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        customer_id INTEGER,
        invoice_date DATE DEFAULT CURRENT_DATE,
        due_date DATE,
        subtotal REAL DEFAULT 0,
        tax_amount REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        total_amount REAL DEFAULT 0,
        paid_amount REAL DEFAULT 0,
        remaining_amount REAL DEFAULT 0,
        payment_method TEXT,
        status TEXT DEFAULT 'pending',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `);

    // جدول تفاصيل الفواتير
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        discount_percentage REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    `);

    // جدول المدفوعات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        customer_id INTEGER,
        amount REAL NOT NULL,
        payment_method TEXT NOT NULL,
        payment_date DATE DEFAULT CURRENT_DATE,
        reference_number TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id),
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `);

    // جدول حركة المخزون
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS stock_movements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
        quantity REAL NOT NULL,
        reference_type TEXT, -- 'invoice', 'purchase', 'adjustment'
        reference_id INTEGER,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    `);

    // جدول المستخدمين
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        permissions TEXT, -- JSON string
        last_login DATETIME,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الإعدادات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول سجل العمليات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        table_name TEXT,
        record_id INTEGER,
        old_values TEXT, -- JSON string
        new_values TEXT, -- JSON string
        ip_address TEXT,
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    console.log('✅ تم إنشاء جميع الجداول بنجاح');
  }

  // ===== إدراج البيانات الافتراضية =====
  insertDefaultData() {
    try {
      // إدراج بيانات الشركة الافتراضية
      const companyExists = this.db.prepare('SELECT COUNT(*) as count FROM company').get();
      if (companyExists.count === 0) {
        this.db.prepare(`
          INSERT INTO company (name, commercial_register, tax_number, phone, email, address)
          VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          'شركة التجارة المتقدمة',
          '1234567890',
          '300123456789003',
          '0112345678',
          '<EMAIL>',
          'الرياض، المملكة العربية السعودية'
        );
      }

      // إدراج الفئات الافتراضية
      const categories = [
        { name: 'أدوات كهربائية', description: 'جميع الأدوات والمعدات الكهربائية', icon: 'electrical_services' },
        { name: 'مواد بناء', description: 'مواد البناء والإنشاءات', icon: 'construction' },
        { name: 'سباكة', description: 'أدوات ومواد السباكة', icon: 'plumbing' },
        { name: 'دهانات', description: 'الدهانات ومواد التشطيب', icon: 'format_paint' },
        { name: 'أدوات يدوية', description: 'الأدوات اليدوية والمعدات', icon: 'build' },
        { name: 'إضاءة', description: 'أنظمة الإضاءة والمصابيح', icon: 'lightbulb' },
        { name: 'أمان وحماية', description: 'معدات الأمان والحماية', icon: 'security' },
        { name: 'متنوعة', description: 'منتجات متنوعة أخرى', icon: 'category' }
      ];

      const categoryExists = this.db.prepare('SELECT COUNT(*) as count FROM categories').get();
      if (categoryExists.count === 0) {
        const insertCategory = this.db.prepare('INSERT INTO categories (name, description, icon) VALUES (?, ?, ?)');
        categories.forEach(category => {
          insertCategory.run(category.name, category.description, category.icon);
        });
      }

      // إدراج منتجات تجريبية
      const productsExist = this.db.prepare('SELECT COUNT(*) as count FROM products').get();
      if (productsExist.count === 0) {
        const sampleProducts = [
          {
            name: 'كابل كهربائي 2.5 مم',
            code: 'ELEC-001',
            category_id: 1,
            description: 'كابل كهربائي عالي الجودة 2.5 مم',
            purchase_price: 18,
            sale_price: 25,
            stock_quantity: 150,
            min_stock: 50,
            max_stock: 200,
            unit: 'متر'
          },
          {
            name: 'أسمنت بورتلاندي',
            code: 'CONST-002',
            category_id: 2,
            description: 'أسمنت بورتلاندي عالي الجودة',
            purchase_price: 22,
            sale_price: 28,
            stock_quantity: 8,
            min_stock: 20,
            max_stock: 100,
            unit: 'كيس'
          },
          {
            name: 'أنبوب PVC 4 بوصة',
            code: 'PLUMB-003',
            category_id: 3,
            description: 'أنبوب PVC قطر 4 بوصة',
            purchase_price: 35,
            sale_price: 45,
            stock_quantity: 45,
            min_stock: 20,
            max_stock: 60,
            unit: 'قطعة'
          }
        ];

        const insertProduct = this.db.prepare(`
          INSERT INTO products (name, code, category_id, description, purchase_price, sale_price,
                               stock_quantity, min_stock, max_stock, unit)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        sampleProducts.forEach(product => {
          insertProduct.run(
            product.name, product.code, product.category_id, product.description,
            product.purchase_price, product.sale_price, product.stock_quantity,
            product.min_stock, product.max_stock, product.unit
          );
        });
      }

      // إدراج عملاء تجريبيين
      const customersExist = this.db.prepare('SELECT COUNT(*) as count FROM customers').get();
      if (customersExist.count === 0) {
        const sampleCustomers = [
          {
            name: 'محمد أحمد السعيد',
            phone: '0501234567',
            email: '<EMAIL>',
            address: 'حي النخيل، الرياض',
            city: 'الرياض',
            customer_type: 'مميز'
          },
          {
            name: 'سعد الأحمد',
            phone: '0509876543',
            email: '<EMAIL>',
            address: 'حي الصفا، جدة',
            city: 'جدة',
            customer_type: 'عادي'
          },
          {
            name: 'عبدالله محمد',
            phone: '0551122334',
            email: '<EMAIL>',
            address: 'حي الدانة، الدمام',
            city: 'الدمام',
            customer_type: 'تجاري'
          }
        ];

        const insertCustomer = this.db.prepare(`
          INSERT INTO customers (name, phone, email, address, city, customer_type)
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        sampleCustomers.forEach(customer => {
          insertCustomer.run(
            customer.name, customer.phone, customer.email,
            customer.address, customer.city, customer.customer_type
          );
        });
      }

      // إدراج الإعدادات الافتراضية
      const settingsExist = this.db.prepare('SELECT COUNT(*) as count FROM settings').get();
      if (settingsExist.count === 0) {
        const defaultSettings = [
          { key: 'language', value: 'ar', description: 'لغة النظام' },
          { key: 'currency', value: 'SAR', description: 'العملة الافتراضية' },
          { key: 'timezone', value: 'GMT+3', description: 'المنطقة الزمنية' },
          { key: 'tax_rate', value: '15', description: 'معدل الضريبة %' },
          { key: 'invoice_prefix', value: 'INV', description: 'بادئة رقم الفاتورة' },
          { key: 'backup_enabled', value: 'true', description: 'تفعيل النسخ الاحتياطي' },
          { key: 'notifications_enabled', value: 'true', description: 'تفعيل الإشعارات' }
        ];

        const insertSetting = this.db.prepare('INSERT INTO settings (key, value, description) VALUES (?, ?, ?)');
        defaultSettings.forEach(setting => {
          insertSetting.run(setting.key, setting.value, setting.description);
        });
      }

      console.log('✅ تم إدراج البيانات الافتراضية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إدراج البيانات الافتراضية:', error);
    }
  }

  // ===== وظائف المنتجات =====
  getAllProducts() {
    return this.db.prepare(`
      SELECT p.*, c.name as category_name, c.icon as category_icon
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.created_at DESC
    `).all();
  }

  getProductById(id) {
    return this.db.prepare(`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `).get(id);
  }

  addProduct(product) {
    return this.db.prepare(`
      INSERT INTO products (name, code, category_id, description, purchase_price, sale_price,
                           stock_quantity, min_stock, max_stock, unit, barcode, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      product.name, product.code, product.category_id, product.description,
      product.purchase_price, product.sale_price, product.stock_quantity,
      product.min_stock, product.max_stock, product.unit, product.barcode, product.status
    );
  }

  updateProduct(id, product) {
    return this.db.prepare(`
      UPDATE products SET
        name = ?, code = ?, category_id = ?, description = ?, purchase_price = ?,
        sale_price = ?, stock_quantity = ?, min_stock = ?, max_stock = ?,
        unit = ?, barcode = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      product.name, product.code, product.category_id, product.description,
      product.purchase_price, product.sale_price, product.stock_quantity,
      product.min_stock, product.max_stock, product.unit, product.barcode,
      product.status, id
    );
  }

  deleteProduct(id) {
    return this.db.prepare('DELETE FROM products WHERE id = ?').run(id);
  }

  // ===== وظائف العملاء =====
  getAllCustomers() {
    return this.db.prepare('SELECT * FROM customers ORDER BY created_at DESC').all();
  }

  getCustomerById(id) {
    return this.db.prepare('SELECT * FROM customers WHERE id = ?').get(id);
  }

  addCustomer(customer) {
    return this.db.prepare(`
      INSERT INTO customers (name, phone, email, address, city, customer_type, tax_number, credit_limit)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      customer.name, customer.phone, customer.email, customer.address,
      customer.city, customer.customer_type, customer.tax_number, customer.credit_limit
    );
  }

  updateCustomer(id, customer) {
    return this.db.prepare(`
      UPDATE customers SET
        name = ?, phone = ?, email = ?, address = ?, city = ?,
        customer_type = ?, tax_number = ?, credit_limit = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      customer.name, customer.phone, customer.email, customer.address,
      customer.city, customer.customer_type, customer.tax_number,
      customer.credit_limit, id
    );
  }

  deleteCustomer(id) {
    return this.db.prepare('DELETE FROM customers WHERE id = ?').run(id);
  }

  // ===== وظائف الفئات =====
  getAllCategories() {
    return this.db.prepare('SELECT * FROM categories ORDER BY name').all();
  }

  addCategory(category) {
    return this.db.prepare('INSERT INTO categories (name, description, icon) VALUES (?, ?, ?)').run(
      category.name, category.description, category.icon
    );
  }

  // ===== وظائف الإعدادات =====
  getSetting(key) {
    return this.db.prepare('SELECT value FROM settings WHERE key = ?').get(key);
  }

  setSetting(key, value) {
    return this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `).run(key, value);
  }

  // ===== إغلاق قاعدة البيانات =====
  close() {
    if (this.db) {
      this.db.close();
      console.log('✅ تم إغلاق قاعدة البيانات');
    }
  }
}

module.exports = SalesDatabase;
