// ===== إضافة عملاء تجريبيين إضافيين =====

const SalesDatabase = require('./src/js/database');

console.log('🚀 بدء إضافة عملاء تجريبيين...');

async function addSampleCustomers() {
  try {
    // إنشاء قاعدة البيانات
    const database = new SalesDatabase();
    
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // عملاء إضافيين متنوعين
    const additionalCustomers = [
      // أفراد
      { fullName: 'سارة أحمد محمد', idNumber: '111222333444', phone: '0501111111', region: 'الرياض' },
      { fullName: 'خالد عبدالله سعد', idNumber: '222333444555', phone: '0502222222', region: 'جدة' },
      { fullName: 'نورا محمد علي', idNumber: '333444555666', phone: '0503333333', region: 'الدمام' },
      { fullName: 'عمر حسن أحمد', idNumber: '444555666777', phone: '0504444444', region: 'مكة' },
      { fullName: 'ليلى سعد محمد', idNumber: '555666777888', phone: '0505555555', region: 'المدينة' },
      { fullName: 'يوسف علي حسين', idNumber: '666777888999', phone: '0506666666', region: 'الطائف' },
      { fullName: 'رنا محمود أحمد', idNumber: '777888999000', phone: '0507777777', region: 'الخبر' },
      { fullName: 'طارق عبدالرحمن', idNumber: '888999000111', phone: '0508888888', region: 'القصيم' },
      { fullName: 'هند فهد العتيبي', idNumber: '999000111222', phone: '0509999999', region: 'حائل' },
      { fullName: 'ماجد سلطان الغامدي', idNumber: '000111222333', phone: '0500000000', region: 'أبها' },
      
      // شركات ومؤسسات
      { fullName: 'شركة البناء المتطور', idNumber: '100200300400', phone: '0511111111', region: 'الرياض' },
      { fullName: 'مؤسسة الإنشاءات الحديثة', idNumber: '200300400500', phone: '0512222222', region: 'جدة' },
      { fullName: 'شركة التشييد والتعمير', idNumber: '300400500600', phone: '0513333333', region: 'الدمام' },
      { fullName: 'مقاولات الخليج للبناء', idNumber: '400500600700', phone: '0514444444', region: 'مكة' },
      { fullName: 'شركة الأعمال الكهربائية', idNumber: '500600700800', phone: '0515555555', region: 'المدينة' },
      { fullName: 'مؤسسة السباكة المتقدمة', idNumber: '600700800900', phone: '0516666666', region: 'الطائف' },
      { fullName: 'شركة الدهانات والديكور', idNumber: '700800900000', phone: '0517777777', region: 'الخبر' },
      { fullName: 'مقاولات الأساسات', idNumber: '800900000100', phone: '0518888888', region: 'القصيم' },
      { fullName: 'شركة المواد الإنشائية', idNumber: '900000100200', phone: '0519999999', region: 'حائل' },
      { fullName: 'مؤسسة التجارة العامة', idNumber: '000100200300', phone: '0510000000', region: 'أبها' },
      
      // عملاء من مناطق أخرى
      { fullName: 'فيصل بن سعود', idNumber: '123123123123', phone: '0521111111', region: 'تبوك' },
      { fullName: 'منى عبدالعزيز', idNumber: '234234234234', phone: '0522222222', region: 'جازان' },
      { fullName: 'سلمان الأحمد', idNumber: '345345345345', phone: '0523333333', region: 'نجران' },
      { fullName: 'ريم محمد القحطاني', idNumber: '456456456456', phone: '0524444444', region: 'الباحة' },
      { fullName: 'عبدالله الزهراني', idNumber: '567567567567', phone: '0525555555', region: 'عسير' },
      { fullName: 'أمل سعد الغامدي', idNumber: '678678678678', phone: '0526666666', region: 'الجوف' },
      { fullName: 'محمد العنزي', idNumber: '789789789789', phone: '0527777777', region: 'الحدود الشمالية' },
      { fullName: 'فاطمة الشهري', idNumber: '890890890890', phone: '0528888888', region: 'عسير' },
      { fullName: 'أحمد المطيري', idNumber: '901901901901', phone: '0529999999', region: 'حفر الباطن' },
      { fullName: 'نادية الحربي', idNumber: '012012012012', phone: '0520000000', region: 'ينبع' }
    ];
    
    console.log(`👥 إضافة ${additionalCustomers.length} عميل جديد...`);
    
    // إضافة العملاء واحد تلو الآخر
    let addedCount = 0;
    for (const customer of additionalCustomers) {
      try {
        const result = database.addCustomer(customer);
        addedCount++;
        console.log(`✅ تم إضافة: ${customer.fullName} (${customer.region})`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️ العميل موجود مسبقاً: ${customer.fullName}`);
        } else {
          console.error(`❌ خطأ في إضافة ${customer.fullName}:`, error.message);
        }
      }
    }
    
    // عرض إحصائيات نهائية
    const allCustomers = database.getAllCustomers();
    console.log('');
    console.log('📊 إحصائيات العملاء:');
    console.log(`   👥 إجمالي العملاء: ${allCustomers.length}`);
    console.log(`   ✅ تم إضافة: ${addedCount} عميل جديد`);
    
    // تجميع حسب المنطقة
    const regions = {};
    allCustomers.forEach(customer => {
      if (!regions[customer.region]) {
        regions[customer.region] = 0;
      }
      regions[customer.region]++;
    });
    
    console.log('');
    console.log('🗺️ العملاء حسب المنطقة:');
    Object.entries(regions).forEach(([region, count]) => {
      console.log(`   📍 ${region}: ${count} عميل`);
    });
    
    // إغلاق قاعدة البيانات
    database.close();
    
    console.log('');
    console.log('🎉 تم إضافة العملاء التجريبيين بنجاح!');
    console.log('💡 يمكنك الآن اختيار من مجموعة كبيرة من العملاء في نقطة البيع');
    
  } catch (error) {
    console.error('❌ خطأ في إضافة العملاء:', error);
  }
}

// تشغيل إضافة العملاء
addSampleCustomers();
