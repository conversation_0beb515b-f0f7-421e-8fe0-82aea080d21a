// ===== إضافة منتجات تجريبية إضافية =====

const SalesDatabase = require('./src/js/database');

console.log('🚀 بدء إضافة منتجات تجريبية...');

async function addSampleProducts() {
  try {
    // إنشاء قاعدة البيانات
    const database = new SalesDatabase();
    
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // منتجات إضافية متنوعة
    const additionalProducts = [
      // أدوات كهربائية
      { name: 'شاكوش كهربائي', price: 45000, category: 'أدوات', icon: 'construction', stock: 35, unit: 'قطعة', barcode: '1111111111111', purchasePrice: 38000 },
      { name: 'منشار دائري', price: 120000, category: 'أدوات', icon: 'carpenter', stock: 15, unit: 'قطعة', barcode: '2222222222222', purchasePrice: 100000 },
      { name: 'مفك براغي كهربائي', price: 35000, category: 'أدوات', icon: 'build', stock: 40, unit: 'قطعة', barcode: '3333333333333', purchasePrice: 28000 },
      { name: 'جلاخة زاوية', price: 65000, category: 'أدوات', icon: 'settings', stock: 20, unit: 'قطعة', barcode: '4444444444444', purchasePrice: 52000 },
      
      // كهربائيات ومواد كهربائية
      { name: 'كابل كهربائي 4مم', price: 25000, category: 'كهربائيات', icon: 'cable', stock: 80, unit: 'متر', barcode: '5555555555555', purchasePrice: 20000 },
      { name: 'مفتاح مزدوج', price: 12000, category: 'كهربائيات', icon: 'toggle_on', stock: 150, unit: 'قطعة', barcode: '6666666666666', purchasePrice: 9000 },
      { name: 'مصباح LED 40 واط', price: 28000, category: 'كهربائيات', icon: 'lightbulb', stock: 60, unit: 'قطعة', barcode: '7777777777777', purchasePrice: 22000 },
      { name: 'قابس كهربائي', price: 8000, category: 'كهربائيات', icon: 'power', stock: 200, unit: 'قطعة', barcode: '8888888888888', purchasePrice: 6000 },
      { name: 'لمبة توفير طاقة', price: 15000, category: 'كهربائيات', icon: 'eco', stock: 90, unit: 'قطعة', barcode: '9999999999999', purchasePrice: 12000 },
      
      // مواد بناء
      { name: 'أسمنت رمادي', price: 14000, category: 'مواد بناء', icon: 'foundation', stock: 120, unit: 'كيس', barcode: '1010101010101', purchasePrice: 11500 },
      { name: 'رمل بناء', price: 8000, category: 'مواد بناء', icon: 'landscape', stock: 200, unit: 'كيس', barcode: '1212121212121', purchasePrice: 6500 },
      { name: 'حصى بناء', price: 10000, category: 'مواد بناء', icon: 'terrain', stock: 150, unit: 'كيس', barcode: '1313131313131', purchasePrice: 8000 },
      { name: 'طوب أبيض', price: 600, category: 'مواد بناء', icon: 'view_module', stock: 800, unit: 'قطعة', barcode: '1414141414141', purchasePrice: 450 },
      { name: 'بلاط سيراميك', price: 35000, category: 'مواد بناء', icon: 'grid_view', stock: 50, unit: 'متر مربع', barcode: '1515151515151', purchasePrice: 28000 },
      
      // دهانات ومواد تشطيب
      { name: 'دهان أزرق', price: 25000, category: 'دهانات', icon: 'format_paint', stock: 45, unit: 'علبة', barcode: '1616161616161', purchasePrice: 20000 },
      { name: 'دهان أخضر', price: 25000, category: 'دهانات', icon: 'format_paint', stock: 40, unit: 'علبة', barcode: '1717171717171', purchasePrice: 20000 },
      { name: 'برايمر أساس', price: 30000, category: 'دهانات', icon: 'brush', stock: 35, unit: 'علبة', barcode: '1818181818181', purchasePrice: 24000 },
      { name: 'فرشاة دهان كبيرة', price: 12000, category: 'دهانات', icon: 'brush', stock: 70, unit: 'قطعة', barcode: '1919191919191', purchasePrice: 9000 },
      { name: 'رولة دهان', price: 8000, category: 'دهانات', icon: 'roller_shades', stock: 85, unit: 'قطعة', barcode: '2020202020202', purchasePrice: 6000 },
      
      // أدوات يدوية
      { name: 'شاكوش يدوي', price: 18000, category: 'أدوات يدوية', icon: 'handyman', stock: 60, unit: 'قطعة', barcode: '2121212121212', purchasePrice: 14000 },
      { name: 'مفك عادي كبير', price: 8000, category: 'أدوات يدوية', icon: 'build', stock: 100, unit: 'قطعة', barcode: '2222222222223', purchasePrice: 6000 },
      { name: 'مفك عادي صغير', price: 5000, category: 'أدوات يدوية', icon: 'build', stock: 120, unit: 'قطعة', barcode: '2323232323232', purchasePrice: 3500 },
      { name: 'كماشة', price: 15000, category: 'أدوات يدوية', icon: 'plumbing', stock: 45, unit: 'قطعة', barcode: '2424242424242', purchasePrice: 12000 },
      { name: 'مقص حديد', price: 22000, category: 'أدوات يدوية', icon: 'content_cut', stock: 30, unit: 'قطعة', barcode: '2525252525252', purchasePrice: 18000 },
      
      // مسامير ومواد تثبيت
      { name: 'مسامير خشب', price: 3000, category: 'مسامير', icon: 'hardware', stock: 300, unit: 'علبة', barcode: '2626262626262', purchasePrice: 2200 },
      { name: 'مسامير حديد طويلة', price: 4000, category: 'مسامير', icon: 'hardware', stock: 250, unit: 'علبة', barcode: '2727272727272', purchasePrice: 3000 },
      { name: 'براغي معدنية', price: 5000, category: 'مسامير', icon: 'precision_manufacturing', stock: 200, unit: 'علبة', barcode: '2828282828282', purchasePrice: 3800 },
      { name: 'صواميل', price: 2500, category: 'مسامير', icon: 'settings', stock: 400, unit: 'علبة', barcode: '2929292929292', purchasePrice: 1800 },
      
      // أنابيب وسباكة
      { name: 'أنبوب PVC 4 بوصة', price: 18000, category: 'سباكة', icon: 'plumbing', stock: 50, unit: 'متر', barcode: '3030303030303', purchasePrice: 14000 },
      { name: 'أنبوب PVC 2 بوصة', price: 12000, category: 'سباكة', icon: 'plumbing', stock: 80, unit: 'متر', barcode: '3131313131313', purchasePrice: 9000 },
      { name: 'كوع PVC', price: 3000, category: 'سباكة', icon: 'turn_right', stock: 150, unit: 'قطعة', barcode: '3232323232323', purchasePrice: 2200 },
      { name: 'صمام مياه', price: 25000, category: 'سباكة', icon: 'water_drop', stock: 40, unit: 'قطعة', barcode: '3333333333334', purchasePrice: 20000 }
    ];
    
    console.log(`📦 إضافة ${additionalProducts.length} منتج جديد...`);
    
    // إضافة المنتجات واحد تلو الآخر
    let addedCount = 0;
    for (const product of additionalProducts) {
      try {
        const result = database.addProduct(product);
        addedCount++;
        console.log(`✅ تم إضافة: ${product.name} (${product.price} د.ع)`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️ المنتج موجود مسبقاً: ${product.name}`);
        } else {
          console.error(`❌ خطأ في إضافة ${product.name}:`, error.message);
        }
      }
    }
    
    // عرض إحصائيات نهائية
    const allProducts = database.getAllProducts();
    console.log('');
    console.log('📊 إحصائيات المنتجات:');
    console.log(`   📦 إجمالي المنتجات: ${allProducts.length}`);
    console.log(`   ✅ تم إضافة: ${addedCount} منتج جديد`);
    
    // تجميع حسب الفئة
    const categories = {};
    allProducts.forEach(product => {
      if (!categories[product.category]) {
        categories[product.category] = 0;
      }
      categories[product.category]++;
    });
    
    console.log('');
    console.log('📋 المنتجات حسب الفئة:');
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`   🏷️ ${category}: ${count} منتج`);
    });
    
    // إغلاق قاعدة البيانات
    database.close();
    
    console.log('');
    console.log('🎉 تم إضافة المنتجات التجريبية بنجاح!');
    console.log('💡 يمكنك الآن رؤية جميع المنتجات في صفحة نقطة البيع');
    
  } catch (error) {
    console.error('❌ خطأ في إضافة المنتجات:', error);
  }
}

// تشغيل إضافة المنتجات
addSampleProducts();
