// ===== اختبار صفحة المبيعات =====

const SalesDatabase = require('./src/js/database');

console.log('🚀 بدء اختبار صفحة المبيعات...');

async function testSalesPage() {
  try {
    // إنشاء قاعدة البيانات
    const database = new SalesDatabase();
    
    console.log('✅ تم إنشاء قاعدة البيانات بنجاح');
    
    // اختبار جلب العملاء
    const customers = database.getAllCustomers();
    console.log(`📋 عدد العملاء: ${customers.length}`);
    
    if (customers.length > 0) {
      console.log('👤 أول عميل:', {
        id: customers[0].id,
        name: customers[0].full_name,
        phone: customers[0].phone,
        region: customers[0].region
      });
    }
    
    // اختبار جلب المنتجات
    const products = database.getAllProducts();
    console.log(`📦 عدد المنتجات: ${products.length}`);
    
    if (products.length > 0) {
      console.log('🛍️ أول منتج:', {
        id: products[0].id,
        name: products[0].name,
        price: products[0].price,
        stock: products[0].stock,
        category: products[0].category
      });
    }
    
    // اختبار إنشاء فاتورة تجريبية
    if (customers.length > 0 && products.length > 0) {
      console.log('📝 اختبار إنشاء فاتورة...');
      
      const testInvoice = {
        invoiceNumber: `TEST-${Date.now()}`,
        customerId: customers[0].id,
        customerName: customers[0].full_name,
        total: products[0].price * 2,
        paidAmount: products[0].price * 2,
        remainingAmount: 0,
        status: 'paid',
        paymentMethod: 'cash',
        notes: 'فاتورة اختبار',
        items: [{
          productId: products[0].id,
          name: products[0].name,
          quantity: 2,
          price: products[0].price,
          total: products[0].price * 2
        }]
      };
      
      const invoiceId = database.saveInvoice(testInvoice);
      console.log(`✅ تم إنشاء فاتورة اختبار برقم: ${invoiceId}`);
      
      // اختبار جلب فواتير العميل
      const customerInvoices = database.getCustomerInvoices(customers[0].id);
      console.log(`📄 عدد فواتير العميل: ${customerInvoices.length}`);
    }
    
    // اختبار الإحصائيات
    const stats = database.getDailyStats();
    console.log('📊 إحصائيات اليوم:', {
      totalInvoices: stats.total_invoices,
      totalSales: stats.total_sales,
      averageSale: stats.average_sale
    });
    
    // اختبار المنتجات منخفضة المخزون
    const lowStock = database.getLowStockProducts(50);
    console.log(`⚠️ منتجات منخفضة المخزون: ${lowStock.length}`);
    
    if (lowStock.length > 0) {
      console.log('📉 منتجات تحتاج تجديد مخزون:');
      lowStock.forEach(product => {
        console.log(`   - ${product.name}: ${product.stock} ${product.unit}`);
      });
    }
    
    // إغلاق قاعدة البيانات
    database.close();
    
    console.log('🎉 تم اختبار صفحة المبيعات بنجاح!');
    console.log('');
    console.log('📋 ملخص النتائج:');
    console.log(`   ✅ العملاء: ${customers.length}`);
    console.log(`   ✅ المنتجات: ${products.length}`);
    console.log(`   ✅ الفواتير اليوم: ${stats.total_invoices}`);
    console.log(`   ✅ إجمالي المبيعات: ${stats.total_sales} د.ع`);
    console.log(`   ⚠️ منتجات منخفضة المخزون: ${lowStock.length}`);
    
  } catch (error) {
    console.error('❌ خطأ في اختبار صفحة المبيعات:', error);
  }
}

// تشغيل الاختبار
testSalesPage();
