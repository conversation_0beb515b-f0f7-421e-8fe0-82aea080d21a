// ===== سجل العميل =====

let currentCustomer = null;
let customerData = null;
let currentFilter = 'all';
let database = null;
let customerId = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
  try {
    console.log('🔄 بدء تهيئة صفحة سجل العميل...');

    // الحصول على معرف العميل من URL
    customerId = getCustomerIdFromURL();
    if (!customerId) {
      showError('معرف العميل غير صحيح');
      return;
    }

    // تهيئة قاعدة البيانات
    await initializeDatabase();

    // تحميل بيانات العميل
    await loadCustomerData(customerId);

    // إعداد مستمعي الأحداث
    setupEventListeners();

    console.log('✅ تم تهيئة صفحة سجل العميل بنجاح');

  } catch (error) {
    console.error('❌ خطأ في تهيئة صفحة سجل العميل:', error);
    showError('حدث خطأ في تحميل بيانات العميل');
  }
});

// الحصول على معرف العميل من URL
function getCustomerIdFromURL() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('id');
}

// تهيئة قاعدة البيانات
async function initializeDatabase() {
  try {
    if (window.DatabaseAPI) {
      database = window.DatabaseAPI;
      console.log('✅ تم الاتصال بقاعدة البيانات');
    } else {
      throw new Error('DatabaseAPI غير متاح');
    }
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    throw error;
  }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // تبويبات السجل
  document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      switchTab(this.dataset.tab);
    });
  });

  // أزرار الفلترة
  document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      filterInvoices(this.dataset.status);
    });
  });
}

// تحميل بيانات العميل
async function loadCustomerData(customerId) {
  try {
    console.log('🔄 تحميل بيانات العميل:', customerId);

    // جلب بيانات العميل من قاعدة البيانات
    currentCustomer = await database.getCustomerById(customerId);
    if (!currentCustomer) {
      throw new Error('العميل غير موجود');
    }

    // تحميل البيانات المالية
    await loadFinancialData(customerId);

    // عرض معلومات العميل
    displayCustomerInfo();

    // عرض الملخص المالي
    displayFinancialSummary();

    // عرض الفواتير
    displayInvoices();

    // عرض المدفوعات
    displayPayments();

    console.log('✅ تم تحميل بيانات العميل بنجاح');

  } catch (error) {
    console.error('❌ خطأ في تحميل بيانات العميل:', error);
    throw error;
  }
}

// تحميل البيانات المالية
async function loadFinancialData(customerId) {
  try {
    // جلب فواتير العميل
    const invoices = await database.getCustomerInvoices(customerId);

    // جلب مدفوعات العميل (إذا كانت متوفرة)
    let payments = [];
    try {
      payments = await database.getCustomerPayments(customerId);
    } catch (error) {
      console.warn('لا توجد وظيفة لجلب المدفوعات:', error);
    }

    // حساب الإحصائيات
    const totalAmount = invoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
    const totalPaid = invoices.reduce((sum, inv) => sum + (inv.paid || 0), 0);
    const totalDebt = totalAmount - totalPaid;
    const pendingInvoices = invoices.filter(inv => (inv.total - inv.paid) > 0).length;

    customerData = {
      totalInvoices: invoices.length,
      totalAmount: totalAmount,
      totalPaid: totalPaid,
      totalDebt: totalDebt,
      pendingInvoices: pendingInvoices,
      invoices: invoices,
      payments: payments
    };

    console.log('📊 البيانات المالية:', customerData);

  } catch (error) {
    console.error('❌ خطأ في تحميل البيانات المالية:', error);
    // استخدام بيانات افتراضية في حالة الخطأ
    customerData = {
      totalInvoices: 0,
      totalAmount: 0,
      totalPaid: 0,
      totalDebt: 0,
      pendingInvoices: 0,
      invoices: [],
      payments: []
    };
  }
}

// عرض معلومات العميل
function displayCustomerInfo() {
  // تحديث العنوان
  const nameElement = document.getElementById('customerName');
  if (nameElement) {
    nameElement.textContent = `سجل العميل - ${currentCustomer.name}`;
  }

  const infoElement = document.getElementById('customerInfo');
  if (infoElement) {
    infoElement.textContent = `${currentCustomer.phone} | ${currentCustomer.region}`;
  }

  // تحديث المعلومات الكاملة
  const fullNameElement = document.getElementById('customerFullName');
  if (fullNameElement) {
    fullNameElement.textContent = currentCustomer.name;
  }

  const phoneElement = document.getElementById('customerPhone');
  if (phoneElement) {
    phoneElement.textContent = currentCustomer.phone;
  }

  const idNumberElement = document.getElementById('customerIdNumber');
  if (idNumberElement) {
    idNumberElement.textContent = currentCustomer.id_number;
  }

  const regionElement = document.getElementById('customerRegion');
  if (regionElement) {
    regionElement.textContent = currentCustomer.region;
  }

  const joinDateElement = document.getElementById('customerJoinDate');
  if (joinDateElement) {
    joinDateElement.textContent = formatDate(currentCustomer.created_at);
  }

  // تحديث نوع العميل
  const isCompany = currentCustomer.type === 'company';
  const typeElement = document.getElementById('customerType');
  if (typeElement) {
    typeElement.textContent = getCustomerTypeText(currentCustomer.type);
    typeElement.className = `customer-type-badge ${isCompany ? 'company' : 'individual'}`;
  }

  const typeIconElement = document.getElementById('customerTypeIcon');
  if (typeIconElement) {
    typeIconElement.textContent = isCompany ? 'business' : 'person';
  }

  // عرض البريد الإلكتروني إذا كان متوفراً
  const emailElement = document.getElementById('customerEmail');
  const emailContainer = document.getElementById('customerEmailContainer');
  if (currentCustomer.email && emailElement && emailContainer) {
    emailElement.textContent = currentCustomer.email;
    emailContainer.style.display = 'flex';
  }
}

// تحويل نوع العميل إلى نص
function getCustomerTypeText(type) {
  const types = {
    'individual': 'عميل فرد',
    'company': 'عميل تجاري',
    'vip': 'عميل مميز'
  };
  return types[type] || 'عميل عادي';
}

// عرض الملخص المالي
function displayFinancialSummary() {
  document.getElementById('totalDebt').textContent = formatPrice(customerData.totalDebt);
  document.getElementById('totalInvoices').textContent = customerData.totalInvoices;
  document.getElementById('totalPaid').textContent = formatPrice(customerData.totalPaid);
  document.getElementById('pendingInvoices').textContent = customerData.pendingInvoices;
}

// عرض الفواتير
function displayInvoices() {
  const grid = document.getElementById('invoicesGrid');

  if (!grid) {
    console.warn('عنصر invoicesGrid غير موجود');
    return;
  }

  if (!customerData.invoices || customerData.invoices.length === 0) {
    grid.innerHTML = `
      <div class="empty-state">
        <span class="material-icons">receipt_long</span>
        <h3>لا توجد فواتير</h3>
        <p>لم يتم إنشاء أي فواتير لهذا العميل بعد</p>
        <button class="btn btn-primary" onclick="createNewInvoice()">
          إنشاء أول فاتورة
        </button>
      </div>
    `;
    return;
  }

  let filteredInvoices = customerData.invoices;
  if (currentFilter !== 'all') {
    const statusMap = {
      'paid': (inv) => (inv.total - inv.paid) === 0,
      'partial': (inv) => inv.paid > 0 && (inv.total - inv.paid) > 0,
      'unpaid': (inv) => inv.paid === 0
    };

    if (statusMap[currentFilter]) {
      filteredInvoices = customerData.invoices.filter(statusMap[currentFilter]);
    }
  }

  grid.innerHTML = filteredInvoices.map(invoice => {
    const remaining = (invoice.total || 0) - (invoice.paid || 0);
    const status = remaining === 0 ? 'paid' : (invoice.paid > 0 ? 'partial' : 'unpaid');

    return `
      <div class="invoice-card ${status}">
        <div class="invoice-header">
          <span class="invoice-number">فاتورة #${invoice.id}</span>
          <div class="invoice-actions">
            <span class="invoice-status ${status}">
              ${getInvoiceStatusText(status)}
            </span>
            <button class="btn-icon print-btn" onclick="printInvoice('${invoice.id}')" title="طباعة الفاتورة">
              <span class="material-icons">print</span>
            </button>
          </div>
        </div>

        <div class="invoice-details" onclick="openInvoiceDetails('${invoice.id}')">
          <div class="invoice-detail">
            <label>التاريخ</label>
            <span>${formatDate(invoice.date)}</span>
          </div>
          <div class="invoice-detail">
            <label>المجموع</label>
            <span>${formatPrice(invoice.total)}</span>
          </div>
          <div class="invoice-detail">
            <label>المدفوع</label>
            <span>${formatPrice(invoice.paid || 0)}</span>
          </div>
          <div class="invoice-detail">
            <label>المتبقي</label>
            <span class="${remaining > 0 ? 'debt' : 'paid'}">${formatPrice(remaining)}</span>
          </div>
        </div>

        ${invoice.notes ? `<div class="invoice-notes" onclick="openInvoiceDetails('${invoice.id}')">${invoice.notes}</div>` : ''}

        ${remaining > 0 ? `
          <div class="invoice-actions-bottom">
            <button class="btn btn-success btn-sm" onclick="addPaymentForInvoice('${invoice.id}')">
              <span class="material-icons">payment</span>
              تسديد
            </button>
          </div>
        ` : ''}
      </div>
    `;
  }).join('');
}

// الحصول على نص حالة الفاتورة
function getInvoiceStatusText(status) {
  const statusTexts = {
    'paid': 'مدفوعة',
    'partial': 'مدفوعة جزئياً',
    'unpaid': 'غير مدفوعة'
  };
  return statusTexts[status] || status;
}

// عرض المدفوعات
function displayPayments() {
  const list = document.getElementById('paymentsList');

  if (!customerData.payments || customerData.payments.length === 0) {
    list.innerHTML = `
      <div class="empty-state">
        <span class="material-icons">payments</span>
        <h3>لا توجد مدفوعات</h3>
        <p>لم يتم تسجيل أي مدفوعات لهذا العميل بعد</p>
      </div>
    `;
    return;
  }

  list.innerHTML = customerData.payments.map(payment => `
    <div class="payment-item">
      <div class="payment-header">
        <span class="payment-number">${payment.id}</span>
        <span class="payment-amount">${formatPrice(payment.amount)}</span>
      </div>

      <div class="payment-details">
        <div class="payment-detail">
          <label>التاريخ</label>
          <span>${formatDate(payment.date)}</span>
        </div>
        <div class="payment-detail">
          <label>الفاتورة</label>
          <span>${payment.invoiceId || 'توزيع تلقائي'}</span>
        </div>
      </div>

      ${payment.notes ? `<div class="payment-notes">ملاحظات: ${payment.notes}</div>` : ''}
    </div>
  `).join('');
}

// تبديل التبويبات
function switchTab(tabName) {
  // إزالة الفئة النشطة من جميع التبويبات
  document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
  document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

  // إضافة الفئة النشطة للتبويب المحدد
  document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
  document.getElementById(`${tabName}-tab`).classList.add('active');
}

// فلترة الفواتير
function filterInvoices(status) {
  currentFilter = status;

  // تحديث أزرار الفلترة
  document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
  document.querySelector(`[data-status="${status}"]`).classList.add('active');

  // إعادة عرض الفواتير
  displayInvoices();
}

// طباعة فاتورة محددة
function printInvoice(invoiceId) {
  const invoice = customerData.invoices.find(inv => inv.id === invoiceId);
  if (!invoice) return;

  // إنشاء نافذة طباعة
  const printWindow = window.open('', '_blank');
  const printContent = generateInvoicePrintContent(invoice);

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
}

// إنشاء محتوى طباعة الفاتورة
function generateInvoicePrintContent(invoice) {
  return `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>فاتورة ${invoice.id}</title>
      <style>
        body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; }
        .invoice-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .invoice-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
        .customer-info, .invoice-details { flex: 1; }
        .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .items-table th, .items-table td { border: 1px solid #ddd; padding: 10px; text-align: center; }
        .items-table th { background: #f5f5f5; }
        .total-section { margin-top: 20px; text-align: left; }
        .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
        .total-final { font-weight: bold; font-size: 18px; border-top: 2px solid #333; padding-top: 10px; }
        .notes { margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="invoice-header">
        <h1>فاتورة مبيعات</h1>
        <h2>${invoice.id}</h2>
      </div>

      <div class="invoice-info">
        <div class="customer-info">
          <h3>معلومات العميل</h3>
          <p><strong>الاسم:</strong> ${currentCustomer.fullName}</p>
          <p><strong>الهاتف:</strong> ${currentCustomer.phone}</p>
          <p><strong>المنطقة:</strong> ${currentCustomer.region}</p>
        </div>
        <div class="invoice-details">
          <h3>تفاصيل الفاتورة</h3>
          <p><strong>التاريخ:</strong> ${formatDate(invoice.date)}</p>
          <p><strong>نوع الدفع:</strong> ${invoice.payment_method === 'cash' ? 'نقدي' : 'آجل'}</p>
          <p><strong>الحالة:</strong> ${getStatusText(invoice.payment_method || invoice.status)}</p>
        </div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>المنتج</th>
            <th>الكمية</th>
            <th>السعر</th>
            <th>المجموع</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>${formatPrice(item.price)}</td>
              <td>${formatPrice(item.total)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="total-section">
        <div class="total-row">
          <span>المجموع الكلي:</span>
          <span>${formatPrice(invoice.total)}</span>
        </div>
        <div class="total-row">
          <span>المدفوع:</span>
          <span>${formatPrice(invoice.paid_amount || 0)}</span>
        </div>
        <div class="total-row total-final">
          <span>المتبقي:</span>
          <span>${formatPrice(invoice.remaining_amount || 0)}</span>
        </div>
      </div>

      ${invoice.notes ? `
        <div class="notes">
          <h4>ملاحظات:</h4>
          <p>${invoice.notes}</p>
        </div>
      ` : ''}

      <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
        تم إنشاء هذه الفاتورة بواسطة نظام إدارة المبيعات
      </div>
    </body>
    </html>
  `;
}

// فتح تفاصيل الفاتورة
function openInvoiceDetails(invoiceId) {
  // فتح نافذة تفاصيل الفاتورة
  window.openInvoiceDetails(invoiceId);
}

// فتح نافذة التسديد
function openPaymentModal() {
  if (currentCustomer && currentCustomer.totalDebt > 0) {
    window.openPaymentModal(currentCustomer.id);
  } else {
    showNotification('لا يوجد دين على هذا العميل', 'info');
  }
}

// طباعة سجل العميل
function printCustomerRecord() {
  window.print();
}

// العودة لصفحة العملاء
function goBack() {
  window.location.href = 'customers.html';
}

// إنشاء فاتورة جديدة
function createNewInvoice() {
  window.location.href = `sales.html?customer=${customerId}`;
}

// تعديل العميل
function editCustomer() {
  if (window.editCustomer) {
    window.editCustomer(customerId);
  } else {
    console.log('تعديل العميل:', customerId);
    // يمكن إضافة نافذة تعديل هنا
  }
}

// عرض تفاصيل الفاتورة
function openInvoiceDetails(invoiceId) {
  console.log('عرض تفاصيل الفاتورة:', invoiceId);
  // سيتم تنفيذها لاحقاً
}

// إضافة دفعة لفاتورة محددة
function addPaymentForInvoice(invoiceId) {
  console.log('إضافة دفعة للفاتورة:', invoiceId);
  // سيتم تنفيذها لاحقاً
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
  const invoice = customerData.invoices.find(inv => inv.id == invoiceId);
  if (!invoice) {
    console.error('الفاتورة غير موجودة:', invoiceId);
    return;
  }

  // إنشاء نافذة طباعة
  const printWindow = window.open('', '_blank');
  const printContent = generateInvoicePrintContent(invoice);

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
}

// وظائف مساعدة
function getCustomerById(id) {
  // محاكاة الحصول على العميل من قاعدة البيانات
  const customers = [
    {
      id: '1',
      fullName: 'محمد أحمد حسين',
      phone: '07901234567',
      region: 'بغداد',
      totalDebt: 150000,
      invoices: [
        {
          id: 'INV-20241201-001',
          date: '2024-12-01',
          total: 75000,
          paid: 0,
          remaining: 75000,
          status: 'pending',
          items: [
            { name: 'مفك كهربائي', quantity: 2, price: 25000, total: 50000 },
            { name: 'مفتاح كهربائي', quantity: 3, price: 8000, total: 24000 }
          ],
          notes: 'فاتورة آجلة - دفع خلال أسبوع'
        }
      ],
      payments: [
        {
          id: 'PAY-001',
          date: '2024-11-30',
          amount: 10000,
          invoiceId: 'INV-20241128-002',
          notes: 'دفعة جزئية'
        }
      ]
    }
  ];

  return customers.find(c => c.id === id);
}

function getStatusText(status) {
  const statusMap = {
    'pending': 'معلقة',
    'partial': 'جزئية',
    'paid': 'مدفوعة',
    'cash': 'نقدية',
    'credit': 'آجلة'
  };
  return statusMap[status] || status;
}

function formatPrice(price) {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price) + ' د.ع';
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('ar-IQ');
}

function showError(message) {
  alert(message);
  window.location.href = 'customers.html';
}

function showNotification(message, type = 'info') {
  // إنشاء إشعار
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-weight: 500;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}
