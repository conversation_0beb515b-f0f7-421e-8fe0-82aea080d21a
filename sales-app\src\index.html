<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المبيعات والحسابات</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/sidebar.css">
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="styles/sales.css">
    <link rel="stylesheet" href="styles/customers.css">
    <link rel="stylesheet" href="styles/inventory.css">
    <link rel="stylesheet" href="styles/products.css">
    <link rel="stylesheet" href="styles/reports.css">
    <link rel="stylesheet" href="styles/settings.css">
    <link rel="stylesheet" href="styles/modals.css">
    <link rel="stylesheet" href="styles/advanced-features.css">
    <!-- خطوط عربية جميلة -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- أيقونات Material Design -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="material-icons">store</span>
                    <h2>نظام المبيعات</h2>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="#dashboard" class="nav-link" data-page="dashboard">
                            <span class="material-icons">dashboard</span>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#sales" class="nav-link" data-page="sales">
                            <span class="material-icons">point_of_sale</span>
                            <span class="nav-text">المبيعات والفواتير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#inventory" class="nav-link" data-page="inventory">
                            <span class="material-icons">inventory_2</span>
                            <span class="nav-text">إدارة المخزون</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#customers" class="nav-link" data-page="customers">
                            <span class="material-icons">people</span>
                            <span class="nav-text">العملاء والموردين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" class="nav-link" data-page="reports">
                            <span class="material-icons">analytics</span>
                            <span class="nav-text">التقارير المالية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#products" class="nav-link" data-page="products">
                            <span class="material-icons">category</span>
                            <span class="nav-text">إدارة المنتجات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-page="settings">
                            <span class="material-icons">settings</span>
                            <span class="nav-text">الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <span class="material-icons">account_circle</span>
                    <div class="user-details">
                        <span class="user-name">المدير العام</span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- شريط العلوي -->
            <header class="top-header">
                <div class="header-left">
                    <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
                </div>
                <div class="header-right">
                    <div class="header-info">
                        <span class="current-date" id="currentDate"></span>
                        <span class="current-time" id="currentTime"></span>
                    </div>
                    <div class="header-actions">
                        <button class="btn-icon" title="الإشعارات">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                        <button class="btn-icon" title="البحث">
                            <span class="material-icons">search</span>
                        </button>
                        <button class="btn-icon" title="ملء الشاشة" onclick="toggleFullscreen()">
                            <span class="material-icons">fullscreen</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- منطقة المحتوى - سيتم تحميل الصفحات هنا -->
            <div class="content-area" id="contentArea">
                <iframe id="pageFrame" src="pages/dashboard.html" frameborder="0" style="width: 100%; height: 100%; border: none;"></iframe>
            </div>
        </main>
    </div>

    <!-- نافذة الإشعارات -->
    <div class="notifications-container" id="notificationsContainer"></div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/database-status.js"></script>
    <script src="js/simple-database-api.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/modals/product-modal.js"></script>
    <script src="js/modals/customer-modal.js"></script>
    <script src="js/modals/delete-modal.js"></script>
    <script src="js/modals/category-modal.js"></script>
    <script src="js/modals/invoice-modal.js"></script>
    <script src="js/invoice-system.js"></script>
    <script src="js/advanced-reports.js"></script>
    <script src="js/backup-system.js"></script>
    <script src="js/user-management.js"></script>
    <script src="js/main.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/products.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
