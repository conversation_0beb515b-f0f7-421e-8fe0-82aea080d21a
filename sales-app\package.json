{"name": "sales-management-system", "version": "1.0.0", "description": "نظام إدارة المبيعات والحسابات - نظام شامل لإدارة المبيعات والمخزون والعملاء", "main": "main-simple.js", "homepage": ".", "scripts": {"start": "electron .", "dev": "electron . --enable-logging", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "build:all": "electron-builder --win --mac --linux", "rebuild": "npm rebuild better-sqlite3", "clean": "<PERSON><PERSON><PERSON> dist build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["sales", "management", "accounting", "pos", "inventory", "customers", "reports", "arabic", "electron", "desktop"], "author": {"name": "Sales Management Team", "email": "<EMAIL>"}, "license": "MIT", "build": {"appId": "com.salesmanagement.app", "productName": "نظام إدارة المبيعات", "directories": {"output": "dist"}, "files": ["src/**/*", "main-simple.js", "package.json", "assets/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^36.3.1", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"better-sqlite3": "^11.10.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/sales-management-system.git"}, "bugs": {"url": "https://github.com/yourusername/sales-management-system/issues"}}