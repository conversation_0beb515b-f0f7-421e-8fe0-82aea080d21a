{"name": "electron-rebuild", "version": "3.2.9", "description": "Electron supporting package to rebuild native node modules against the currently installed electron", "main": "lib/src/main.js", "typings": "lib/src/main.d.ts", "scripts": {"codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "compile": "tsc", "coverage": "nyc npm run spec", "watch": "tsc -w", "prepare": "npm run compile", "mocha": "cross-env TS_NODE_FILES=true mocha", "lint": "eslint --ext .ts .", "spec": "npm run mocha -- test/*.ts", "test": "npm run lint && npm run spec"}, "bin": {"electron-rebuild": "lib/src/cli.js"}, "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/electron/electron-rebuild"}, "keywords": ["electron"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/electron/electron-rebuild/issues"}, "homepage": "https://github.com/electron/electron-rebuild", "engines": {"node": ">=12.13.0"}, "dependencies": {"@malept/cross-spawn-promise": "^2.0.0", "chalk": "^4.0.0", "debug": "^4.1.1", "detect-libc": "^2.0.1", "fs-extra": "^10.0.0", "got": "^11.7.0", "lzma-native": "^8.0.5", "node-abi": "^3.0.0", "node-api-version": "^0.1.4", "node-gyp": "^9.0.0", "ora": "^5.1.0", "semver": "^7.3.5", "tar": "^6.0.5", "yargs": "^17.0.1"}, "devDependencies": {"@continuous-auth/semantic-release-npm": "^2.0.0", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/chai": "^4.2.12", "@types/chai-as-promised": "^7.1.3", "@types/debug": "^4.1.5", "@types/fs-extra": "^9.0.1", "@types/lzma-native": "^4.0.0", "@types/mocha": "^9.0.0", "@types/node": "^17.0.8", "@types/node-abi": "^3.0.0", "@types/semver": "^7.3.9", "@types/tar": "^6.1.0", "@types/yargs": "^17.0.2", "@typescript-eslint/eslint-plugin": "^4.0.1", "@typescript-eslint/parser": "^4.0.1", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "codecov": "^3.7.2", "cross-env": "^7.0.2", "electron": "^12.0.2", "eslint": "^7.7.0", "eslint-plugin-mocha": "^9.0.0", "mocha": "^9.0.1", "nyc": "^15.1.0", "semantic-release": "^17.1.1", "ts-node": "^10.0.0", "typescript": "^4.0.2"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2019, "sourceType": "module"}, "plugins": ["mocha", "@typescript-eslint"], "env": {"es6": true, "mocha": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"mocha/no-exclusive-tests": "error", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"args": "after-used", "argsIgnorePattern": "^_", "ignoreRestSiblings": true, "vars": "all"}]}}, "eslintIgnore": ["*.d.ts", "node_modules"], "mocha": {"extensions": ["ts"], "require": "ts-node/register"}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript"}}