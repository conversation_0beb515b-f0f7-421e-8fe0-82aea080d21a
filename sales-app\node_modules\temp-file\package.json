{"name": "temp-file", "version": "3.4.0", "main": "out/main.js", "author": "<PERSON>", "license": "MIT", "repository": "develar/temp-file", "bugs": "https://github.com/develar/temp-file/issues", "homepage": "https://github.com/develar/temp-file", "files": ["out"], "scripts": {"compile": "tsc", "release": "pnpm compile && pnpm publish --no-git-checks", "update-deps": "pnpm update -i -r --latest"}, "dependencies": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}, "devDependencies": {"@types/fs-extra": "^9.0.11", "@types/js-yaml": "^4.0.1", "@types/node": "^15.0.2", "typescript": "^4.2.4"}, "typings": "./out/main.d.ts"}