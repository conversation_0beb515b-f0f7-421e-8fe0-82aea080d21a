// ===== معالج حالة قاعدة البيانات =====

class DatabaseStatusManager {
  constructor() {
    this.currentStatus = 'disconnected';
    this.statusIndicator = null;
    this.loadingOverlay = null;
    this.init();
  }

  // تهيئة معالج حالة قاعدة البيانات
  init() {
    // إنشاء مؤشر حالة قاعدة البيانات
    this.createStatusIndicator();
    
    // إنشاء شاشة التحميل
    this.createLoadingOverlay();
    
    // تسجيل معالجات الأحداث
    this.setupEventListeners();
    
    // التحقق من حالة قاعدة البيانات الحالية
    this.checkDatabaseStatus();
  }

  // إنشاء مؤشر حالة قاعدة البيانات
  createStatusIndicator() {
    // إنشاء عنصر مؤشر الحالة
    this.statusIndicator = document.createElement('div');
    this.statusIndicator.id = 'database-status-indicator';
    this.statusIndicator.className = 'database-status disconnected';
    this.statusIndicator.innerHTML = `
      <div class="status-icon">
        <span class="material-icons">storage</span>
      </div>
      <div class="status-text">
        <span class="status-title">قاعدة البيانات</span>
        <span class="status-message">غير متصل</span>
      </div>
    `;

    // إضافة الأنماط
    const style = document.createElement('style');
    style.textContent = `
      .database-status {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 1000;
        min-width: 200px;
        transition: all 0.3s ease;
        border-left: 4px solid #ccc;
      }

      .database-status.connected {
        border-left-color: #4CAF50;
        background: #f8fff8;
      }

      .database-status.error {
        border-left-color: #f44336;
        background: #fff8f8;
        animation: shake 0.5s ease-in-out;
      }

      .database-status.connecting {
        border-left-color: #ff9800;
        background: #fffaf0;
      }

      .status-icon .material-icons {
        font-size: 24px;
        color: #666;
      }

      .database-status.connected .status-icon .material-icons {
        color: #4CAF50;
      }

      .database-status.error .status-icon .material-icons {
        color: #f44336;
      }

      .database-status.connecting .status-icon .material-icons {
        color: #ff9800;
        animation: pulse 1s infinite;
      }

      .status-text {
        display: flex;
        flex-direction: column;
        gap: 2px;
      }

      .status-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;
      }

      .status-message {
        font-size: 12px;
        color: #666;
      }

      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
      }

      .loading-content {
        text-align: center;
        padding: 40px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        max-width: 400px;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #2196F3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .loading-message {
        font-size: 14px;
        color: #666;
      }

      .hidden {
        display: none !important;
      }
    `;
    document.head.appendChild(style);

    // إضافة المؤشر للصفحة
    document.body.appendChild(this.statusIndicator);
  }

  // إنشاء شاشة التحميل
  createLoadingOverlay() {
    this.loadingOverlay = document.createElement('div');
    this.loadingOverlay.className = 'loading-overlay hidden';
    this.loadingOverlay.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-title">تطبيق إدارة المبيعات</div>
        <div class="loading-message">جاري تهيئة قاعدة البيانات...</div>
      </div>
    `;
    document.body.appendChild(this.loadingOverlay);
  }

  // تسجيل معالجات الأحداث
  setupEventListeners() {
    if (window.electronAPI) {
      // معالج حالة قاعدة البيانات
      window.electronAPI.onDatabaseStatus((data) => {
        this.updateStatus(data.status, data.title, data.message);
      });

      // معالج إظهار شاشة التحميل
      window.electronAPI.onShowLoading((data) => {
        this.showLoading(data.message);
      });

      // معالج إخفاء شاشة التحميل
      window.electronAPI.onHideLoading(() => {
        this.hideLoading();
      });
    }
  }

  // التحقق من حالة قاعدة البيانات
  async checkDatabaseStatus() {
    if (window.electronAPI) {
      try {
        const status = await window.electronAPI.getDatabaseStatus();
        this.updateStatus(status.status, 'حالة قاعدة البيانات', 
          `العملاء: ${status.customersCount} | المنتجات: ${status.productsCount}`);
      } catch (error) {
        console.error('خطأ في التحقق من حالة قاعدة البيانات:', error);
        this.updateStatus('error', 'خطأ في الاتصال', 'فشل في التحقق من حالة قاعدة البيانات');
      }
    }
  }

  // تحديث حالة قاعدة البيانات
  updateStatus(status, title, message) {
    this.currentStatus = status;
    
    if (this.statusIndicator) {
      // تحديث الفئة
      this.statusIndicator.className = `database-status ${status}`;
      
      // تحديث النص
      const titleElement = this.statusIndicator.querySelector('.status-title');
      const messageElement = this.statusIndicator.querySelector('.status-message');
      
      if (titleElement) titleElement.textContent = title;
      if (messageElement) messageElement.textContent = message;

      // إظهار المؤشر لفترة ثم إخفاؤه (إلا في حالة الخطأ)
      this.statusIndicator.style.display = 'flex';
      
      if (status === 'connected') {
        setTimeout(() => {
          if (this.statusIndicator) {
            this.statusIndicator.style.opacity = '0.7';
          }
        }, 3000);
        
        setTimeout(() => {
          if (this.statusIndicator && this.currentStatus === 'connected') {
            this.statusIndicator.style.display = 'none';
          }
        }, 8000);
      }
    }

    // إظهار إشعار في وحدة التحكم
    console.log(`📊 حالة قاعدة البيانات: ${status} - ${title}: ${message}`);
  }

  // إظهار شاشة التحميل
  showLoading(message = 'جاري التحميل...') {
    if (this.loadingOverlay) {
      const messageElement = this.loadingOverlay.querySelector('.loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
      this.loadingOverlay.classList.remove('hidden');
    }
  }

  // إخفاء شاشة التحميل
  hideLoading() {
    if (this.loadingOverlay) {
      this.loadingOverlay.classList.add('hidden');
    }
  }

  // الحصول على الحالة الحالية
  getCurrentStatus() {
    return this.currentStatus;
  }

  // تدمير المعالج
  destroy() {
    if (window.electronAPI) {
      window.electronAPI.removeAllListeners('database-status');
      window.electronAPI.removeAllListeners('show-loading');
      window.electronAPI.removeAllListeners('hide-loading');
    }
    
    if (this.statusIndicator) {
      this.statusIndicator.remove();
    }
    
    if (this.loadingOverlay) {
      this.loadingOverlay.remove();
    }
  }
}

// إنشاء مثيل عام لمعالج حالة قاعدة البيانات
let databaseStatusManager = null;

// تهيئة المعالج عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  databaseStatusManager = new DatabaseStatusManager();
});

// تصدير المعالج للاستخدام العام
window.DatabaseStatusManager = DatabaseStatusManager;
window.databaseStatusManager = databaseStatusManager;
