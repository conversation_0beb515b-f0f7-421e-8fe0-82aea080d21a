// ===== واجهة برمجة التطبيقات لقاعدة البيانات =====

const { ipcRenderer } = require('electron');

class DatabaseAPI {
  // ===== وظائف المنتجات =====
  
  async getAllProducts() {
    try {
      return await ipcRenderer.invoke('get-all-products');
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      throw error;
    }
  }

  async getProductById(id) {
    try {
      return await ipcRenderer.invoke('get-product-by-id', id);
    } catch (error) {
      console.error('خطأ في جلب المنتج:', error);
      throw error;
    }
  }

  async addProduct(product) {
    try {
      return await ipcRenderer.invoke('add-product', product);
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  }

  async updateProduct(id, product) {
    try {
      return await ipcRenderer.invoke('update-product', id, product);
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      throw error;
    }
  }

  async deleteProduct(id) {
    try {
      return await ipcRenderer.invoke('delete-product', id);
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      throw error;
    }
  }

  // ===== وظائف العملاء =====
  
  async getAllCustomers() {
    try {
      return await ipcRenderer.invoke('get-all-customers');
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error);
      throw error;
    }
  }

  async getCustomerById(id) {
    try {
      return await ipcRenderer.invoke('get-customer-by-id', id);
    } catch (error) {
      console.error('خطأ في جلب العميل:', error);
      throw error;
    }
  }

  async addCustomer(customer) {
    try {
      return await ipcRenderer.invoke('add-customer', customer);
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  }

  async updateCustomer(id, customer) {
    try {
      return await ipcRenderer.invoke('update-customer', id, customer);
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  }

  async deleteCustomer(id) {
    try {
      return await ipcRenderer.invoke('delete-customer', id);
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  }

  // ===== وظائف الفئات =====
  
  async getAllCategories() {
    try {
      return await ipcRenderer.invoke('get-all-categories');
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      throw error;
    }
  }

  async addCategory(category) {
    try {
      return await ipcRenderer.invoke('add-category', category);
    } catch (error) {
      console.error('خطأ في إضافة الفئة:', error);
      throw error;
    }
  }

  // ===== وظائف الإعدادات =====
  
  async getSetting(key) {
    try {
      return await ipcRenderer.invoke('get-setting', key);
    } catch (error) {
      console.error('خطأ في جلب الإعداد:', error);
      throw error;
    }
  }

  async setSetting(key, value) {
    try {
      return await ipcRenderer.invoke('set-setting', key, value);
    } catch (error) {
      console.error('خطأ في حفظ الإعداد:', error);
      throw error;
    }
  }

  // ===== وظائف مساعدة =====
  
  async getProductsWithLowStock() {
    try {
      const products = await this.getAllProducts();
      return products.filter(product => product.stock_quantity <= product.min_stock);
    } catch (error) {
      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
      throw error;
    }
  }

  async getProductsByCategory(categoryId) {
    try {
      const products = await this.getAllProducts();
      return products.filter(product => product.category_id === categoryId);
    } catch (error) {
      console.error('خطأ في جلب منتجات الفئة:', error);
      throw error;
    }
  }

  async searchProducts(searchTerm) {
    try {
      const products = await this.getAllProducts();
      const term = searchTerm.toLowerCase();
      return products.filter(product => 
        product.name.toLowerCase().includes(term) ||
        product.code.toLowerCase().includes(term) ||
        product.description?.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('خطأ في البحث عن المنتجات:', error);
      throw error;
    }
  }

  async searchCustomers(searchTerm) {
    try {
      const customers = await this.getAllCustomers();
      const term = searchTerm.toLowerCase();
      return customers.filter(customer => 
        customer.name.toLowerCase().includes(term) ||
        customer.phone?.includes(term) ||
        customer.email?.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('خطأ في البحث عن العملاء:', error);
      throw error;
    }
  }

  // ===== إحصائيات =====
  
  async getInventoryStats() {
    try {
      const products = await this.getAllProducts();
      const categories = await this.getAllCategories();
      
      const totalProducts = products.length;
      const totalValue = products.reduce((sum, product) => 
        sum + (product.stock_quantity * product.purchase_price), 0
      );
      const lowStockItems = products.filter(product => 
        product.stock_quantity <= product.min_stock
      ).length;
      const totalCategories = categories.length;

      return {
        totalProducts,
        totalValue,
        lowStockItems,
        totalCategories
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات المخزون:', error);
      throw error;
    }
  }

  async getCustomersStats() {
    try {
      const customers = await this.getAllCustomers();
      
      const totalCustomers = customers.length;
      const activeCustomers = customers.filter(customer => 
        customer.status === 'active'
      ).length;
      const vipCustomers = customers.filter(customer => 
        customer.customer_type === 'مميز'
      ).length;
      const commercialCustomers = customers.filter(customer => 
        customer.customer_type === 'تجاري'
      ).length;

      return {
        totalCustomers,
        activeCustomers,
        vipCustomers,
        commercialCustomers
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات العملاء:', error);
      throw error;
    }
  }

  // ===== التحقق من صحة البيانات =====
  
  validateProduct(product) {
    const errors = [];
    
    if (!product.name || product.name.trim() === '') {
      errors.push('اسم المنتج مطلوب');
    }
    
    if (!product.code || product.code.trim() === '') {
      errors.push('كود المنتج مطلوب');
    }
    
    if (!product.category_id) {
      errors.push('فئة المنتج مطلوبة');
    }
    
    if (product.purchase_price < 0) {
      errors.push('سعر الشراء يجب أن يكون أكبر من أو يساوي صفر');
    }
    
    if (product.sale_price < 0) {
      errors.push('سعر البيع يجب أن يكون أكبر من أو يساوي صفر');
    }
    
    if (product.stock_quantity < 0) {
      errors.push('كمية المخزون يجب أن تكون أكبر من أو تساوي صفر');
    }
    
    return errors;
  }

  validateCustomer(customer) {
    const errors = [];
    
    if (!customer.name || customer.name.trim() === '') {
      errors.push('اسم العميل مطلوب');
    }
    
    if (customer.phone && !/^[0-9+\-\s()]+$/.test(customer.phone)) {
      errors.push('رقم الهاتف غير صحيح');
    }
    
    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }
    
    if (customer.credit_limit < 0) {
      errors.push('الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر');
    }
    
    return errors;
  }

  // ===== تنسيق البيانات =====
  
  formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  }

  formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
  }

  formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
const dbAPI = new DatabaseAPI();

// تصدير للاستخدام في الملفات الأخرى
window.DatabaseAPI = dbAPI;
