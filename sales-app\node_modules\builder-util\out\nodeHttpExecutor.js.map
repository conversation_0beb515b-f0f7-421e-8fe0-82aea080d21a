{"version": 3, "file": "nodeHttpExecutor.js", "sourceRoot": "", "sources": ["../src/nodeHttpExecutor.ts"], "names": [], "mappings": ";;;AAAA,+DAAmD;AACnD,+BAA4D;AAC5D,uDAAiD;AACjD,+BAA8B;AAC9B,yDAAmD;AAEnD,MAAa,gBAAiB,SAAQ,mCAA2B;IAC/D,mCAAmC;IACnC,qCAAqC;IACrC,aAAa,CAAC,OAAY,EAAE,QAAiC;QAC3D,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC9E,OAAO,CAAC,KAAK,GAAG,IAAI,mCAAe,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAA;QACjE,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACnF,OAAO,CAAC,KAAK,GAAG,IAAI,iCAAc,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;QAC/D,CAAC;QACD,OAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,cAAW,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IACxF,CAAC;CACF;AAXD,4CAWC;AAEY,QAAA,YAAY,GAAG,IAAI,gBAAgB,EAAE,CAAA", "sourcesContent": ["import { HttpExecutor } from \"builder-util-runtime\"\nimport { ClientRequest, request as httpRequest } from \"http\"\nimport { HttpProxyAgent } from \"http-proxy-agent\"\nimport * as https from \"https\"\nimport { HttpsProxyAgent } from \"https-proxy-agent\"\n\nexport class NodeHttpExecutor extends HttpExecutor<ClientRequest> {\n  // noinspection JSMethodCanBeStatic\n  // noinspection JSUnusedGlobalSymbols\n  createRequest(options: any, callback: (response: any) => void): ClientRequest {\n    if (process.env[\"https_proxy\"] !== undefined && options.protocol === \"https:\") {\n      options.agent = new HttpsProxyAgent(process.env[\"https_proxy\"])\n    } else if (process.env[\"http_proxy\"] !== undefined && options.protocol === \"http:\") {\n      options.agent = new HttpProxyAgent(process.env[\"http_proxy\"])\n    }\n    return (options.protocol === \"http:\" ? httpRequest : https.request)(options, callback)\n  }\n}\n\nexport const httpExecutor = new NodeHttpExecutor()\n"]}