# [3.2.0](https://github.com/thenativeweb/boolean/compare/3.1.4...3.2.0) (2022-02-16)


### Features

* Introduce isBooleanable function. ([#341](https://github.com/thenativeweb/boolean/issues/341)) ([e2ecfb3](https://github.com/thenativeweb/boolean/commit/e2ecfb357db729990e533dfd498211cea6126a17))

## [3.1.4](https://github.com/thenativeweb/boolean/compare/3.1.3...3.1.4) (2021-08-19)


### Bug Fixes

* Downgrade workflows to Node 14. ([#319](https://github.com/thenativeweb/boolean/issues/319)) ([072b068](https://github.com/thenativeweb/boolean/commit/072b0685f8de7602f6be9da9b80cda08cdd71778))
* Rollback versions and remove engines field. ([#318](https://github.com/thenativeweb/boolean/issues/318)) ([145dfcf](https://github.com/thenativeweb/boolean/commit/145dfcf7f4c5e3f5898e43661b9a017a2d8cb6a9))

## [3.1.3](https://github.com/thenativeweb/boolean/compare/3.1.2...3.1.3) (2021-08-19)


### Bug Fixes

* bump path-parse from 1.0.6 to 1.0.7 ([#316](https://github.com/thenativeweb/boolean/issues/316)) ([0817f9d](https://github.com/thenativeweb/boolean/commit/0817f9d5c7e4691558e7562146afac19258a655c))

## [3.1.2](https://github.com/thenativeweb/boolean/compare/3.1.1...3.1.2) (2021-06-10)


### Bug Fixes

* bump trim-newlines from 3.0.0 to 3.0.1 ([#302](https://github.com/thenativeweb/boolean/issues/302)) ([376489f](https://github.com/thenativeweb/boolean/commit/376489fe37ec9c46aafb44d3c9abf0edeabc6f93))

## [3.1.1](https://github.com/thenativeweb/boolean/compare/3.1.0...3.1.1) (2021-06-10)


### Bug Fixes

* bump glob-parent from 5.1.1 to 5.1.2 ([#303](https://github.com/thenativeweb/boolean/issues/303)) ([8265437](https://github.com/thenativeweb/boolean/commit/8265437b1b3215256f8649e10ac65d4036a38bad))

# [3.1.0](https://github.com/thenativeweb/boolean/compare/3.0.4...3.1.0) (2021-05-30)


### Features

* Add support for primitive object wrappers (fixes [#295](https://github.com/thenativeweb/boolean/issues/295)) ([#296](https://github.com/thenativeweb/boolean/issues/296)) ([5ae115f](https://github.com/thenativeweb/boolean/commit/5ae115f09f123cdb624452fc163fc8724e0ab926))

## [3.0.4](https://github.com/thenativeweb/boolean/compare/3.0.3...3.0.4) (2021-05-10)


### Bug Fixes

* bump hosted-git-info from 2.8.8 to 2.8.9 ([#289](https://github.com/thenativeweb/boolean/issues/289)) ([69ead2c](https://github.com/thenativeweb/boolean/commit/69ead2c8fe897d546f8329ed262e6158938581be))

## [3.0.3](https://github.com/thenativeweb/boolean/compare/3.0.2...3.0.3) (2021-03-25)


### Bug Fixes

* Migrate from master to main. ([#273](https://github.com/thenativeweb/boolean/issues/273)) ([18b640a](https://github.com/thenativeweb/boolean/commit/18b640af858d26b4dd76b9de443a4039e1e2131a))

## [3.0.2](https://github.com/thenativeweb/boolean/compare/3.0.1...3.0.2) (2020-11-03)


### Bug Fixes

* Fix headline for robot section in readme. ([#191](https://github.com/thenativeweb/boolean/issues/191)) ([6b7b72b](https://github.com/thenativeweb/boolean/commit/6b7b72b6d5d5c1ad2251c5959b35c8c87b3421a5))

## [3.0.1](https://github.com/thenativeweb/boolean/compare/3.0.0...3.0.1) (2020-02-11)


### Bug Fixes

* Simplify comparison code to not use unicode regexp flag ([#99](https://github.com/thenativeweb/boolean/issues/99)) ([2be2aeb](https://github.com/thenativeweb/boolean/commit/2be2aeb244c060eccb388dacc6903bbad193e745))
