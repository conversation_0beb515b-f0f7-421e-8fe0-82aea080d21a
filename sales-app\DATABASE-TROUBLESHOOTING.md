# 🔧 دليل استكشاف أخطاء قاعدة البيانات

## 🚨 المشاكل الشائعة وحلولها

### 1. خطأ NODE_MODULE_VERSION

#### **الخطأ:**
```
The module 'better_sqlite3.node' was compiled against a different Node.js version using NODE_MODULE_VERSION 127. This version of Node.js requires NODE_MODULE_VERSION 135.
```

#### **السبب:**
- إصدار Node.js تم تحديثه بعد تثبيت better-sqlite3
- better-sqlite3 مُجمع لإصدار أقدم من Node.js

#### **الحل:**
```bash
# 1. إعادة بناء better-sqlite3
npm rebuild better-sqlite3

# 2. إذا لم ينجح، أعد التثبيت
npm uninstall better-sqlite3
npm install better-sqlite3

# 3. تحقق من الإصدار
node --version
```

---

### 2. خطأ فتح قاعدة البيانات

#### **الخطأ:**
```
SQLITE_CANTOPEN: unable to open database file
```

#### **الأسباب المحتملة:**
- ملف قاعدة البيانات غير موجود
- صلاحيات غير كافية
- مجلد البيانات غير موجود

#### **الحل:**
```bash
# 1. إنشاء مجلد البيانات
mkdir -p src/data

# 2. تشغيل إنشاء قاعدة البيانات
node restore-database.js

# 3. التحقق من الصلاحيات
# تأكد من صلاحيات الكتابة في مجلد src/data
```

---

### 3. قاعدة البيانات فاسدة

#### **الأعراض:**
- خطأ في قراءة البيانات
- تعطل التطبيق عند الوصول لقاعدة البيانات
- رسائل خطأ غير واضحة

#### **الحل:**
```bash
# 1. إنشاء نسخة احتياطية (إن أمكن)
cp src/data/sales.db src/data/sales.db.backup

# 2. استعادة قاعدة البيانات
node restore-database.js

# 3. إعادة إضافة البيانات التجريبية
node add-sample-products.js
node add-sample-customers.js
```

---

## 🧪 أدوات الاختبار والصيانة

### اختبار شامل للنظام
```bash
node test-startup-sequence.js
```

### اختبار معالجة الأخطاء
```bash
node test-database-error.js
```

### استعادة قاعدة البيانات
```bash
node restore-database.js
```

### إضافة بيانات تجريبية
```bash
node add-sample-products.js
node add-sample-customers.js
```

### اختبار قاعدة البيانات
```bash
node test-complete-system.js
```

---

## 🔍 تشخيص المشاكل

### 1. التحقق من إصدار Node.js
```bash
node --version
# يجب أن يكون 16.0.0 أو أحدث
```

### 2. التحقق من تثبيت better-sqlite3
```bash
npm list better-sqlite3
```

### 3. اختبار قاعدة البيانات يدوياً
```javascript
const SalesDatabase = require('./src/js/database');
try {
  const db = new SalesDatabase();
  console.log('قاعدة البيانات تعمل بشكل صحيح');
  db.close();
} catch (error) {
  console.error('خطأ في قاعدة البيانات:', error);
}
```

---

## 🛠️ خطوات الإصلاح المتقدمة

### إعادة تثبيت كامل
```bash
# 1. حذف node_modules
rm -rf node_modules
rm package-lock.json

# 2. إعادة التثبيت
npm install

# 3. إعادة بناء better-sqlite3
npm rebuild better-sqlite3
```

### تنظيف ذاكرة التخزين المؤقت
```bash
npm cache clean --force
```

### التحقق من متطلبات النظام
- **Node.js**: 16.0.0 أو أحدث
- **npm**: 8.0.0 أو أحدث
- **Python**: مطلوب لتجميع better-sqlite3
- **Visual Studio Build Tools**: في Windows

---

## 📋 قائمة التحقق السريع

عند مواجهة مشاكل في قاعدة البيانات:

- [ ] تحقق من إصدار Node.js
- [ ] أعد بناء better-sqlite3
- [ ] تحقق من وجود مجلد src/data
- [ ] تحقق من صلاحيات الملفات
- [ ] شغل اختبار النظام
- [ ] تحقق من سجلات الأخطاء
- [ ] أعد إنشاء قاعدة البيانات إذا لزم الأمر

---

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **شغل التشخيص الشامل:**
   ```bash
   node test-startup-sequence.js
   ```

2. **احفظ سجل الأخطاء:**
   - انسخ رسائل الخطأ كاملة
   - احفظ إصدار Node.js ونظام التشغيل

3. **جرب الحلول بالترتيب:**
   - إعادة بناء better-sqlite3
   - إعادة تثبيت better-sqlite3
   - إعادة إنشاء قاعدة البيانات
   - إعادة تثبيت كامل

---

## ✅ التحقق من نجاح الإصلاح

بعد تطبيق أي حل:

```bash
# 1. اختبار قاعدة البيانات
node test-startup-sequence.js

# 2. تشغيل التطبيق
node_modules\electron\dist\electron.exe .

# 3. التحقق من البيانات
# يجب أن ترى 35 عميل و 42 منتج
```

إذا رأيت هذه الرسالة، فكل شيء يعمل بشكل صحيح:
```
✅ تهيئة قاعدة البيانات: نجح
✅ بدء التطبيق: نجح
✅ اختبار الوظائف: نجح
```
