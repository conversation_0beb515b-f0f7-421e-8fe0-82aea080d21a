const { app, BrowserWindow, Menu, ipcMain, Notification, dialog } = require('electron');
const path = require('path');
const SalesDatabase = require('./src/js/simple-database');

// متغير للنافذة الرئيسية
let mainWindow;
let database;
let databaseStatus = 'disconnected'; // حالة قاعدة البيانات

// إنشاء النافذة الرئيسية
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'src', 'js', 'preload.js'),
      webSecurity: false // للسماح بتحميل الملفات المحلية
    },
    // icon: path.join(__dirname, 'assets', 'icon.png'), // سيتم إضافة الأيقونة لاحقاً
    show: false,
    titleBarStyle: 'default',
    autoHideMenuBar: false // إظهار شريط القوائم
  });

  // تحميل الملف الرئيسي
  mainWindow.loadFile(path.join(__dirname, 'src', 'index.html'));

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('🚀 تم تشغيل نظام إدارة المبيعات');
  });

  // معالجة أخطاء التحميل
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ فشل في تحميل الصفحة:', errorCode, errorDescription);
  });

  // فتح أدوات المطور دائماً للتشخيص
  mainWindow.webContents.openDevTools();

  // معالجة إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء القائمة
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // سنضيف وظيفة إنشاء جديد لاحقاً
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // سنضيف وظيفة فتح لاحقاً
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // سنضيف وظيفة حفظ لاحقاً
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { role: 'undo', label: 'تراجع' },
        { role: 'redo', label: 'إعادة' },
        { type: 'separator' },
        { role: 'cut', label: 'قص' },
        { role: 'copy', label: 'نسخ' },
        { role: 'paste', label: 'لصق' },
        { role: 'selectall', label: 'تحديد الكل' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { role: 'reload', label: 'إعادة تحميل' },
        { role: 'forceReload', label: 'إعادة تحميل قسري' },
        { role: 'toggleDevTools', label: 'أدوات المطور' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
        { role: 'zoomIn', label: 'تكبير' },
        { role: 'zoomOut', label: 'تصغير' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'ملء الشاشة' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            // سنضيف نافذة "حول التطبيق" لاحقاً
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// إظهار إشعارات قاعدة البيانات
function showDatabaseNotification(type, title, message) {
  console.log(`📢 ${type.toUpperCase()}: ${title} - ${message}`);

  if (type === 'success') {
    databaseStatus = 'connected';

    // إشعار نظام التشغيل
    if (Notification.isSupported()) {
      new Notification({
        title: '✅ قاعدة البيانات',
        body: `${title}\n${message}`,
        icon: path.join(__dirname, 'assets', 'icon.png')
      }).show();
    }

    // إرسال حالة النجاح للواجهة الأمامية
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('database-status', {
        status: 'connected',
        title: title,
        message: message,
        timestamp: new Date().toISOString()
      });
    }

  } else if (type === 'error') {
    databaseStatus = 'error';

    // إشعار خطأ في نظام التشغيل
    if (Notification.isSupported()) {
      new Notification({
        title: '❌ خطأ في قاعدة البيانات',
        body: `${title}\n${message}`,
        icon: path.join(__dirname, 'assets', 'icon.png')
      }).show();
    }

    // نافذة حوار للخطأ الحرج
    if (mainWindow) {
      dialog.showErrorBox('خطأ في قاعدة البيانات',
        `${title}\n\nالتفاصيل: ${message}\n\nسيتم محاولة إعادة الاتصال تلقائياً خلال 5 ثوان.`);
    }

    // إرسال حالة الخطأ للواجهة الأمامية
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('database-status', {
        status: 'error',
        title: title,
        message: message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

// إضافة معالج للحصول على حالة قاعدة البيانات
ipcMain.handle('get-database-status', () => {
  return {
    status: databaseStatus,
    timestamp: new Date().toISOString(),
    customersCount: database ? database.getAllCustomers().length : 0,
    productsCount: database ? database.getAllProducts().length : 0
  };
});

// إظهار شاشة التحميل
function showLoadingScreen() {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('show-loading', {
      message: 'جاري تهيئة قاعدة البيانات...',
      timestamp: new Date().toISOString()
    });
  }
}

// إخفاء شاشة التحميل
function hideLoadingScreen() {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('hide-loading', {
      timestamp: new Date().toISOString()
    });
  }
}

// تهيئة قاعدة البيانات مع معالجة الأخطاء
async function initializeDatabase() {
  try {
    console.log('🔄 بدء تهيئة قاعدة البيانات...');

    // محاولة إنشاء قاعدة البيانات
    database = new SalesDatabase();

    // اختبار الاتصال بقاعدة البيانات (مع await للوظائف async)
    const testQuery = await database.getAllCustomers();
    console.log(`✅ تم الاتصال بقاعدة البيانات بنجاح - العملاء: ${testQuery.length}`);

    // اختبار المنتجات
    const testProducts = await database.getAllProducts();
    console.log(`✅ تم تحميل المنتجات بنجاح - المنتجات: ${testProducts.length}`);

    // إظهار إشعار نجاح
    showDatabaseNotification('success', 'تم الاتصال بقاعدة البيانات بنجاح',
      `العملاء: ${testQuery.length} | المنتجات: ${testProducts.length}`);

    return true;

  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);

    // إظهار إشعار خطأ
    showDatabaseNotification('error', 'فشل في الاتصال بقاعدة البيانات',
      `خطأ: ${error.message}`);

    // لا نعيد المحاولة تلقائياً لتجنب الحلقة اللانهائية
    return false;
  }
}

// إعداد IPC handlers لقاعدة البيانات
function setupDatabaseHandlers() {
  // العملاء
  ipcMain.handle('get-customers', async () => {
    try {
      if (!database) {
        throw new Error('قاعدة البيانات غير متصلة');
      }

      if (typeof database.getAllCustomers !== 'function') {
        console.error('وظائف قاعدة البيانات المتاحة:', Object.keys(database));
        throw new Error('وظيفة getAllCustomers غير موجودة');
      }

      const customers = await database.getAllCustomers();
      console.log('📊 تم جلب العملاء من قاعدة البيانات:', customers?.length || 0);
      return customers;
    } catch (error) {
      console.error('❌ خطأ في جلب العملاء:', error);
      return [];
    }
  });

  ipcMain.handle('add-customer', async (event, customerData) => {
    try {
      return database.addCustomer(customerData);
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  });

  ipcMain.handle('update-customer', async (event, id, customerData) => {
    try {
      return database.updateCustomer(id, customerData);
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  });

  ipcMain.handle('delete-customer', async (event, id) => {
    try {
      return database.deleteCustomer(id);
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  });

  ipcMain.handle('get-customer-by-id', async (event, id) => {
    try {
      return database.getCustomerById(id);
    } catch (error) {
      console.error('خطأ في جلب العميل بالمعرف:', error);
      return null;
    }
  });

  ipcMain.handle('get-customer-by-id-number', async (event, idNumber) => {
    try {
      return database.getCustomerByIdNumber(idNumber);
    } catch (error) {
      console.error('خطأ في البحث عن العميل برقم الهوية:', error);
      return null;
    }
  });

  // المنتجات
  ipcMain.handle('get-products', async () => {
    try {
      return database.getAllProducts();
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      return [];
    }
  });

  ipcMain.handle('add-product', async (event, productData) => {
    try {
      return database.addProduct(productData);
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  });

  // الفواتير
  ipcMain.handle('save-invoice', async (event, invoiceData) => {
    try {
      return database.saveInvoice(invoiceData);
    } catch (error) {
      console.error('خطأ في حفظ الفاتورة:', error);
      throw error;
    }
  });

  ipcMain.handle('get-customer-invoices', async (event, customerId) => {
    try {
      return database.getCustomerInvoices(customerId);
    } catch (error) {
      console.error('خطأ في جلب فواتير العميل:', error);
      return [];
    }
  });

  // الإحصائيات
  ipcMain.handle('get-daily-stats', async () => {
    try {
      return database.getDailyStats();
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      return { total_invoices: 0, total_sales: 0, average_sale: 0 };
    }
  });

  ipcMain.handle('get-low-stock-products', async (event, threshold = 10) => {
    try {
      return database.getLowStockProducts(threshold);
    } catch (error) {
      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
      return [];
    }
  });
}

// عند جاهزية التطبيق
app.whenReady().then(async () => {
  console.log('🚀 بدء تشغيل تطبيق إدارة المبيعات...');

  // إنشاء النافذة أولاً
  createMainWindow();
  createMenu();

  // إظهار شاشة تحميل
  showLoadingScreen();

  // تهيئة قاعدة البيانات
  const dbInitialized = await initializeDatabase();

  if (dbInitialized) {
    // إعداد معالجات قاعدة البيانات
    setupDatabaseHandlers();

    // إخفاء شاشة التحميل وإظهار التطبيق
    hideLoadingScreen();
    console.log('✅ تم تشغيل التطبيق بنجاح');
  } else {
    console.log('⚠️ التطبيق يعمل في وضع محدود - قاعدة البيانات غير متاحة');
  }

  // في macOS، إعادة إنشاء النافذة عند النقر على أيقونة التطبيق
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (database) {
      database.close();
    }
    app.quit();
  }
});

// إغلاق قاعدة البيانات عند إنهاء التطبيق
app.on('before-quit', () => {
  if (database) {
    database.close();
  }
});

// منع إنشاء نوافذ متعددة
app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// التأكد من تشغيل مثيل واحد فقط
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
