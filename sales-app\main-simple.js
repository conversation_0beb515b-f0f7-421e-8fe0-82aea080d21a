const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');
const SalesDatabase = require('./src/js/database');

// متغير للنافذة الرئيسية
let mainWindow;
let database;

// إنشاء النافذة الرئيسية
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'src', 'js', 'preload.js')
    },
    // icon: path.join(__dirname, 'assets', 'icon.png'), // سيتم إضافة الأيقونة لاحقاً
    show: false,
    titleBarStyle: 'default'
  });

  // تحميل الملف الرئيسي
  mainWindow.loadFile(path.join(__dirname, 'src', 'index.html'));

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('🚀 تم تشغيل نظام إدارة المبيعات');
  });

  // فتح أدوات المطور في وضع التطوير
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // معالجة إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء القائمة
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // سنضيف وظيفة إنشاء جديد لاحقاً
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // سنضيف وظيفة فتح لاحقاً
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // سنضيف وظيفة حفظ لاحقاً
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { role: 'undo', label: 'تراجع' },
        { role: 'redo', label: 'إعادة' },
        { type: 'separator' },
        { role: 'cut', label: 'قص' },
        { role: 'copy', label: 'نسخ' },
        { role: 'paste', label: 'لصق' },
        { role: 'selectall', label: 'تحديد الكل' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { role: 'reload', label: 'إعادة تحميل' },
        { role: 'forceReload', label: 'إعادة تحميل قسري' },
        { role: 'toggleDevTools', label: 'أدوات المطور' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
        { role: 'zoomIn', label: 'تكبير' },
        { role: 'zoomOut', label: 'تصغير' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'ملء الشاشة' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            // سنضيف نافذة "حول التطبيق" لاحقاً
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// تهيئة قاعدة البيانات
function initializeDatabase() {
  try {
    database = new SalesDatabase();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
  }
}

// إعداد IPC handlers لقاعدة البيانات
function setupDatabaseHandlers() {
  // العملاء
  ipcMain.handle('get-customers', async () => {
    try {
      return database.getAllCustomers();
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error);
      return [];
    }
  });

  ipcMain.handle('add-customer', async (event, customerData) => {
    try {
      return database.addCustomer(customerData);
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  });

  ipcMain.handle('update-customer', async (event, id, customerData) => {
    try {
      return database.updateCustomer(id, customerData);
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  });

  ipcMain.handle('delete-customer', async (event, id) => {
    try {
      return database.deleteCustomer(id);
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  });

  // المنتجات
  ipcMain.handle('get-products', async () => {
    try {
      return database.getAllProducts();
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      return [];
    }
  });

  ipcMain.handle('add-product', async (event, productData) => {
    try {
      return database.addProduct(productData);
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  });

  // الفواتير
  ipcMain.handle('save-invoice', async (event, invoiceData) => {
    try {
      return database.saveInvoice(invoiceData);
    } catch (error) {
      console.error('خطأ في حفظ الفاتورة:', error);
      throw error;
    }
  });

  ipcMain.handle('get-customer-invoices', async (event, customerId) => {
    try {
      return database.getCustomerInvoices(customerId);
    } catch (error) {
      console.error('خطأ في جلب فواتير العميل:', error);
      return [];
    }
  });

  // الإحصائيات
  ipcMain.handle('get-daily-stats', async () => {
    try {
      return database.getDailyStats();
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      return { total_invoices: 0, total_sales: 0, average_sale: 0 };
    }
  });

  ipcMain.handle('get-low-stock-products', async (event, threshold = 10) => {
    try {
      return database.getLowStockProducts(threshold);
    } catch (error) {
      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
      return [];
    }
  });
}

// عند جاهزية التطبيق
app.whenReady().then(() => {
  initializeDatabase();  // تهيئة قاعدة البيانات أولاً
  createMainWindow();
  createMenu();
  setupDatabaseHandlers();

  // في macOS، إعادة إنشاء النافذة عند النقر على أيقونة التطبيق
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (database) {
      database.close();
    }
    app.quit();
  }
});

// إغلاق قاعدة البيانات عند إنهاء التطبيق
app.on('before-quit', () => {
  if (database) {
    database.close();
  }
});

// منع إنشاء نوافذ متعددة
app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// التأكد من تشغيل مثيل واحد فقط
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
