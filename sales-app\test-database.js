// ===== اختبار قاعدة البيانات =====

const SalesDatabase = require('./src/js/database');

console.log('🚀 بدء اختبار قاعدة البيانات...');

try {
  // إنشاء قاعدة البيانات
  const database = new SalesDatabase();
  
  console.log('✅ تم إنشاء قاعدة البيانات بنجاح');
  
  // اختبار جلب العملاء
  const customers = database.getAllCustomers();
  console.log(`📋 عدد العملاء: ${customers.length}`);
  
  if (customers.length > 0) {
    console.log('👤 أول عميل:', customers[0]);
  }
  
  // اختبار جلب المنتجات
  const products = database.getAllProducts();
  console.log(`📦 عدد المنتجات: ${products.length}`);
  
  if (products.length > 0) {
    console.log('🛍️ أول منتج:', products[0]);
  }
  
  // اختبار الإحصائيات
  const stats = database.getDailyStats();
  console.log('📊 إحصائيات اليوم:', stats);
  
  // اختبار المنتجات منخفضة المخزون
  const lowStock = database.getLowStockProducts(50);
  console.log(`⚠️ منتجات منخفضة المخزون: ${lowStock.length}`);
  
  // إغلاق قاعدة البيانات
  database.close();
  
  console.log('🎉 تم اختبار قاعدة البيانات بنجاح!');
  
} catch (error) {
  console.error('❌ خطأ في اختبار قاعدة البيانات:', error);
}
