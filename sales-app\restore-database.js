// ===== استعادة قاعدة البيانات =====

const fs = require('fs');
const path = require('path');
const SalesDatabase = require('./src/js/database');

console.log('🔧 بدء استعادة قاعدة البيانات...');

function restoreDatabase() {
  const dbPath = path.join(__dirname, 'src', 'data', 'sales.db');
  const backupPath = path.join(__dirname, 'src', 'data', 'sales.db.backup');
  
  try {
    // 1. التحقق من وجود النسخة الاحتياطية
    if (fs.existsSync(backupPath)) {
      console.log('📋 استعادة من النسخة الاحتياطية...');
      
      // حذف الملف الفاسد
      if (fs.existsSync(dbPath)) {
        fs.unlinkSync(dbPath);
        console.log('🗑️ تم حذف الملف الفاسد');
      }
      
      // استعادة النسخة الاحتياطية
      fs.copyFileSync(backupPath, dbPath);
      console.log('✅ تم استعادة النسخة الاحتياطية');
      
      // حذف النسخة الاحتياطية
      fs.unlinkSync(backupPath);
      console.log('🧹 تم حذف النسخة الاحتياطية');
      
    } else {
      console.log('📝 إنشاء قاعدة بيانات جديدة...');
      
      // حذف الملف الفاسد إن وجد
      if (fs.existsSync(dbPath)) {
        fs.unlinkSync(dbPath);
        console.log('🗑️ تم حذف الملف الفاسد');
      }
      
      // إنشاء قاعدة بيانات جديدة
      const database = new SalesDatabase();
      console.log('✅ تم إنشاء قاعدة بيانات جديدة');
      
      // التحقق من البيانات
      const customers = database.getAllCustomers();
      const products = database.getAllProducts();
      
      console.log(`👥 العملاء: ${customers.length}`);
      console.log(`📦 المنتجات: ${products.length}`);
      
      database.close();
    }
    
    // 2. اختبار قاعدة البيانات المستعادة
    console.log('🧪 اختبار قاعدة البيانات المستعادة...');
    const testDatabase = new SalesDatabase();
    
    const testCustomers = testDatabase.getAllCustomers();
    const testProducts = testDatabase.getAllProducts();
    const testStats = testDatabase.getDailyStats();
    
    console.log('');
    console.log('📊 نتائج الاختبار:');
    console.log(`   👥 العملاء: ${testCustomers.length}`);
    console.log(`   📦 المنتجات: ${testProducts.length}`);
    console.log(`   💰 فواتير اليوم: ${testStats.total_invoices}`);
    console.log(`   💵 مبيعات اليوم: ${testStats.total_sales.toLocaleString()} د.ع`);
    
    testDatabase.close();
    
    console.log('');
    console.log('🎉 تم استعادة قاعدة البيانات بنجاح!');
    console.log('💡 يمكنك الآن تشغيل التطبيق بشكل طبيعي:');
    console.log('   node_modules\\electron\\dist\\electron.exe .');
    
  } catch (error) {
    console.error('❌ خطأ في استعادة قاعدة البيانات:', error);
    
    console.log('');
    console.log('🔧 محاولة إنشاء قاعدة بيانات جديدة...');
    
    try {
      // حذف أي ملفات فاسدة
      if (fs.existsSync(dbPath)) {
        fs.unlinkSync(dbPath);
      }
      
      // إنشاء قاعدة بيانات جديدة
      const newDatabase = new SalesDatabase();
      console.log('✅ تم إنشاء قاعدة بيانات جديدة بنجاح');
      
      const customers = newDatabase.getAllCustomers();
      const products = newDatabase.getAllProducts();
      
      console.log(`👥 العملاء: ${customers.length}`);
      console.log(`📦 المنتجات: ${products.length}`);
      
      newDatabase.close();
      
      console.log('🎉 قاعدة البيانات الجديدة جاهزة للاستخدام!');
      
    } catch (newError) {
      console.error('❌ فشل في إنشاء قاعدة بيانات جديدة:', newError);
      console.log('');
      console.log('🆘 يرجى التحقق من:');
      console.log('   1. صلاحيات الكتابة في مجلد src/data');
      console.log('   2. تثبيت better-sqlite3 بشكل صحيح');
      console.log('   3. عدم وجود عمليات أخرى تستخدم قاعدة البيانات');
    }
  }
}

// تشغيل الاستعادة
restoreDatabase();
