// ===== Preload Script =====
// هذا الملف يعمل كجسر بين العملية الرئيسية والعملية المقدمة

const { contextBridge, ipcRenderer } = require('electron');

// تعريض API آمن للعملية المقدمة
contextBridge.exposeInMainWorld('electronAPI', {
  // العملاء
  getCustomers: () => ipcRenderer.invoke('get-customers'),
  addCustomer: (customerData) => ipcRenderer.invoke('add-customer', customerData),
  updateCustomer: (id, customerData) => ipcRenderer.invoke('update-customer', id, customerData),
  deleteCustomer: (id) => ipcRenderer.invoke('delete-customer', id),

  // المنتجات
  getProducts: () => ipcRenderer.invoke('get-products'),
  addProduct: (productData) => ipcRenderer.invoke('add-product', productData),

  // الفواتير
  saveInvoice: (invoiceData) => ipcRenderer.invoke('save-invoice', invoiceData),
  getCustomerInvoices: (customerId) => ipcRenderer.invoke('get-customer-invoices', customerId),

  // وظائف مساعدة
  isElectron: true,
  platform: process.platform
});

console.log('Preload script loaded successfully');
