// ===== Preload Script =====
// هذا الملف يعمل كجسر بين العملية الرئيسية والعملية المقدمة

const { contextBridge, ipcRenderer } = require('electron');

// تعريض API آمن للعملية المقدمة
contextBridge.exposeInMainWorld('electronAPI', {
  // العملاء
  getCustomers: () => ipcRenderer.invoke('get-customers'),
  addCustomer: (customerData) => ipcRenderer.invoke('add-customer', customerData),
  updateCustomer: (id, customerData) => ipcRenderer.invoke('update-customer', id, customerData),
  deleteCustomer: (id) => ipcRenderer.invoke('delete-customer', id),

  // المنتجات
  getProducts: () => ipcRenderer.invoke('get-products'),
  addProduct: (productData) => ipcRenderer.invoke('add-product', productData),

  // الفواتير
  saveInvoice: (invoiceData) => ipcRenderer.invoke('save-invoice', invoiceData),
  getCustomerInvoices: (customerId) => ipcRenderer.invoke('get-customer-invoices', customerId),

  // الإحصائيات
  getDailyStats: () => ipcRenderer.invoke('get-daily-stats'),
  getLowStockProducts: (threshold) => ipcRenderer.invoke('get-low-stock-products', threshold),

  // قاعدة البيانات
  getDatabaseStatus: () => ipcRenderer.invoke('get-database-status'),

  // معالجات الأحداث
  onDatabaseStatus: (callback) => {
    ipcRenderer.on('database-status', (event, data) => callback(data));
  },

  onShowLoading: (callback) => {
    ipcRenderer.on('show-loading', (event, data) => callback(data));
  },

  onHideLoading: (callback) => {
    ipcRenderer.on('hide-loading', (event, data) => callback(data));
  },

  // إزالة معالجات الأحداث
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // وظائف عامة
  showNotification: (title, body) => {
    new Notification(title, { body });
  },

  // وظائف مساعدة
  isElectron: true,
  platform: process.platform
});

console.log('Preload script loaded successfully');
