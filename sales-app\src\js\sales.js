// ===== نقطة البيع مع قاعدة البيانات =====

// تحميل البيانات من قاعدة البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
  await loadSalesData();
  initializeSalesPage();
});

// تحميل البيانات من قاعدة البيانات
async function loadSalesData() {
  try {
    if (window.electronAPI) {
      // تحميل العملاء من قاعدة البيانات
      const dbCustomers = await window.electronAPI.getCustomers();
      console.log('تم تحميل العملاء من قاعدة البيانات:', dbCustomers.length);

      // تحديث قائمة العملاء
      updateCustomersFromDatabase(dbCustomers);

      // تحميل المنتجات من قاعدة البيانات
      const dbProducts = await window.electronAPI.getProducts();
      console.log('تم تحميل المنتجات من قاعدة البيانات:', dbProducts.length);

      // تحديث قائمة المنتجات
      updateProductsFromDatabase(dbProducts);
    }
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
  }
}

// تحديث العملاء من قاعدة البيانات
function updateCustomersFromDatabase(dbCustomers) {
  const customerSelect = document.getElementById('customerSelect');
  if (customerSelect) {
    customerSelect.innerHTML = '<option value="">اختر العميل...</option>';
    dbCustomers.forEach(customer => {
      const option = document.createElement('option');
      option.value = customer.id;
      option.textContent = customer.full_name;
      customerSelect.appendChild(option);
    });
  }
}

// تحديث المنتجات من قاعدة البيانات
function updateProductsFromDatabase(dbProducts) {
  const productsContainer = document.getElementById('productsContainer');
  if (productsContainer && dbProducts.length > 0) {
    productsContainer.innerHTML = '';
    dbProducts.forEach(product => {
      const productCard = createProductCard(product);
      productsContainer.appendChild(productCard);
    });
  }
}

// إنشاء بطاقة منتج
function createProductCard(product) {
  const card = document.createElement('div');
  card.className = 'product-card';
  card.onclick = () => addToInvoice(product);

  card.innerHTML = `
    <div class="product-icon">
      <span class="material-icons">${product.icon || 'inventory_2'}</span>
    </div>
    <div class="product-info">
      <h4>${product.name}</h4>
      <p class="product-price">${formatPrice(product.price)}</p>
      <p class="product-stock">المخزون: ${product.stock || 0}</p>
    </div>
  `;

  return card;
}

// تهيئة صفحة المبيعات
function initializeSalesPage() {
  // تهيئة الأحداث والواجهة
  setupEventListeners();
  calculateTotal();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // مستمع تغيير العميل
  const customerSelect = document.getElementById('customerSelect');
  if (customerSelect) {
    customerSelect.addEventListener('change', function() {
      currentInvoice.customer = this.value || null;
    });
  }
}

// ===== البيانات =====
let currentInvoice = {
  items: [],
  customer: null,
  subtotal: 0,
  tax: 0,
  discount: 0,
  total: 0
};

// قائمة المنتجات
const products = [
  {
    id: '1',
    name: 'مفك كهربائي',
    price: 25000,
    category: 'tools',
    icon: 'build',
    stock: 50,
    unit: 'قطعة',
    barcode: '1234567890123'
  },
  {
    id: '2',
    name: 'مثقاب كهربائي',
    price: 85000,
    category: 'tools',
    icon: 'construction',
    stock: 25,
    unit: 'قطعة',
    barcode: '2345678901234'
  },
  {
    id: '3',
    name: 'كابل كهربائي',
    price: 15000,
    category: 'electronics',
    icon: 'cable',
    stock: 100,
    unit: 'متر',
    barcode: '3456789012345'
  },
  {
    id: '4',
    name: 'مفتاح كهربائي',
    price: 8000,
    category: 'electronics',
    icon: 'toggle_on',
    stock: 200,
    unit: 'قطعة',
    barcode: '4567890123456'
  },
  {
    id: '5',
    name: 'أسمنت',
    price: 12000,
    category: 'materials',
    icon: 'foundation',
    stock: 80,
    unit: 'كيس',
    barcode: '5678901234567'
  },
  {
    id: '6',
    name: 'طوب أحمر',
    price: 500,
    category: 'materials',
    icon: 'view_module',
    stock: 1000,
    unit: 'قطعة',
    barcode: '6789012345678'
  },
  {
    id: '7',
    name: 'مصباح LED',
    price: 18000,
    category: 'electronics',
    icon: 'lightbulb',
    stock: 75,
    unit: 'قطعة',
    barcode: '7890123456789'
  },
  {
    id: '8',
    name: 'منشار يدوي',
    price: 35000,
    category: 'tools',
    icon: 'carpenter',
    stock: 30,
    unit: 'قطعة',
    barcode: '8901234567890'
  }
];

let filteredProducts = [...products];

// ===== وظائف التهيئة =====
function initPOS() {
  renderProducts();
  updateInvoiceDisplay();
}

// ===== وظائف المنتجات =====
function renderProducts() {
  const grid = document.getElementById('productsGrid');
  if (!grid) return;

  if (filteredProducts.length === 0) {
    grid.innerHTML = `
      <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #7f8c8d;">
        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px;">inventory_2</span>
        <p>لا توجد منتجات</p>
      </div>
    `;
    return;
  }

  grid.innerHTML = filteredProducts.map(product => `
    <div class="product-card ${product.stock <= 10 ? 'low-stock' : ''}" onclick="addToInvoice('${product.id}')">
      <div class="product-icon">
        <span class="material-icons">${product.icon}</span>
        ${product.stock <= 10 ? '<span class="stock-warning material-icons">warning</span>' : ''}
      </div>
      <div class="product-name">${product.name}</div>
      <div class="product-price">${formatPrice(product.price)}</div>
      <div class="product-stock">
        <span class="stock-count">${product.stock} ${product.unit}</span>
        <span class="stock-status ${product.stock > 50 ? 'high' : product.stock > 10 ? 'medium' : 'low'}">
          ${product.stock > 50 ? 'متوفر' : product.stock > 10 ? 'قليل' : 'نفاد قريب'}
        </span>
      </div>
      <div class="product-barcode">${product.barcode}</div>
    </div>
  `).join('');
}

// البحث في المنتجات
function searchProducts() {
  const searchTerm = document.getElementById('productSearch').value.toLowerCase();
  filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm)
  );
  renderProducts();
}

// فلترة حسب الفئة
function filterByCategory(category) {
  // تحديث التبويبات
  document.querySelectorAll('.category-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-category="${category}"]`).classList.add('active');

  // فلترة المنتجات
  if (category === 'all') {
    filteredProducts = [...products];
  } else {
    filteredProducts = products.filter(product => product.category === category);
  }

  renderProducts();
}

// ===== وظائف الفاتورة =====
function addToInvoice(productId) {
  const product = products.find(p => p.id === productId);
  if (!product) return;

  // البحث عن المنتج في الفاتورة
  const existingItem = currentInvoice.items.find(item => item.productId === productId);

  if (existingItem) {
    // زيادة الكمية
    existingItem.quantity += 1;
    existingItem.total = existingItem.quantity * existingItem.price;
  } else {
    // إضافة منتج جديد
    currentInvoice.items.push({
      productId: productId,
      name: product.name,
      price: product.price,
      quantity: 1,
      total: product.price
    });
  }

  calculateTotal();
  updateInvoiceDisplay();
}

// حذف منتج من الفاتورة
function removeFromInvoice(productId) {
  currentInvoice.items = currentInvoice.items.filter(item => item.productId !== productId);
  calculateTotal();
  updateInvoiceDisplay();
}

// تحديث كمية المنتج
function updateQuantity(productId, newQuantity) {
  const item = currentInvoice.items.find(item => item.productId === productId);
  if (item && newQuantity > 0) {
    item.quantity = newQuantity;
    item.total = item.quantity * item.price;
    calculateTotal();
    updateInvoiceDisplay();
  }
}

// حساب المجموع
function calculateTotal() {
  currentInvoice.subtotal = currentInvoice.items.reduce((sum, item) => sum + item.total, 0);
  currentInvoice.tax = 0; // لا توجد ضرائب حالياً

  const discountElement = document.getElementById('discount');
  currentInvoice.discount = discountElement ? parseInt(discountElement.value) || 0 : 0;

  currentInvoice.total = currentInvoice.subtotal + currentInvoice.tax - currentInvoice.discount;

  updateTotals();
}

// تحديث عرض المجاميع
function updateTotals() {
  const subtotalElement = document.getElementById('subtotal');
  const taxElement = document.getElementById('tax');
  const totalElement = document.getElementById('total');

  if (subtotalElement) subtotalElement.textContent = formatPrice(currentInvoice.subtotal);
  if (taxElement) taxElement.textContent = formatPrice(currentInvoice.tax);
  if (totalElement) totalElement.textContent = formatPrice(currentInvoice.total);
}

// تحديث عرض الفاتورة
function updateInvoiceDisplay() {
  const invoiceList = document.getElementById('invoiceList');
  if (!invoiceList) return;

  if (currentInvoice.items.length === 0) {
    invoiceList.innerHTML = `
      <div style="text-align: center; padding: 40px; color: #7f8c8d;">
        <span class="material-icons" style="font-size: 32px; margin-bottom: 8px;">shopping_cart</span>
        <p>لا توجد منتجات في الفاتورة</p>
      </div>
    `;
    return;
  }

  invoiceList.innerHTML = currentInvoice.items.map(item => `
    <div class="invoice-item">
      <div class="item-name">${item.name}</div>
      <div class="item-quantity">
        <button class="quantity-btn" onclick="updateQuantity('${item.productId}', ${item.quantity - 1})">-</button>
        <input type="number" class="quantity-input" value="${item.quantity}"
               onchange="updateQuantity('${item.productId}', parseInt(this.value))">
        <button class="quantity-btn" onclick="updateQuantity('${item.productId}', ${item.quantity + 1})">+</button>
      </div>
      <div class="item-price">${formatPrice(item.price)}</div>
      <div class="item-total">${formatPrice(item.total)}</div>
      <button class="remove-btn" onclick="removeFromInvoice('${item.productId}')">
        <span class="material-icons">close</span>
      </button>
    </div>
  `).join('');
}

// ===== وظائف الإجراءات =====
function clearInvoice() {
  if (currentInvoice.items.length === 0) return;

  if (confirm('هل أنت متأكد من مسح الفاتورة؟')) {
    currentInvoice = {
      items: [],
      customer: null,
      subtotal: 0,
      tax: 0,
      discount: 0,
      total: 0
    };

    document.getElementById('customerSelect').value = '';
    document.getElementById('discount').value = '0';

    updateInvoiceDisplay();
    calculateTotal();

    showNotification('تم مسح الفاتورة', 'success');
  }
}

function holdInvoice() {
  if (currentInvoice.items.length === 0) {
    showNotification('لا توجد منتجات في الفاتورة', 'error');
    return;
  }

  // حفظ الفاتورة المعلقة (يمكن تطويرها لاحقاً)
  showNotification('تم تعليق الفاتورة', 'info');
}

function processPayment() {
  // التحقق من وجود منتجات في الفاتورة
  if (currentInvoice.items.length === 0) {
    showNotification('يجب إضافة منتجات للفاتورة أولاً', 'error');
    return;
  }

  // التحقق من صحة المجموع
  if (currentInvoice.total <= 0) {
    showNotification('المجموع يجب أن يكون أكبر من صفر', 'error');
    return;
  }

  // التحقق من اختيار العميل
  if (!currentInvoice.customer) {
    showNotification('يجب اختيار العميل قبل إتمام عملية الدفع', 'error');
    return;
  }

  // فتح نافذة الدفع المنبثقة
  showPaymentModal();
}

// نافذة الدفع المنبثقة
function showPaymentModal() {
  const modalHTML = `
    <div id="paymentModal" class="payment-modal-overlay">
      <div class="payment-modal-container">
        <div class="payment-modal-header">
          <h3>إتمام عملية الدفع</h3>
          <button class="payment-modal-close" onclick="closePaymentModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="payment-modal-body">
          <div class="payment-summary">
            <h4>ملخص الفاتورة</h4>
            <div class="summary-row">
              <span>المجموع الكلي:</span>
              <span class="total-amount">${formatPrice(currentInvoice.total)}</span>
            </div>
          </div>

          <div class="payment-methods">
            <h4>طريقة الدفع</h4>
            <div class="payment-options">
              <label class="payment-option">
                <input type="radio" name="paymentMethod" value="cash" checked onchange="togglePaymentFields()">
                <span class="material-icons">payments</span>
                نقداً
              </label>
              <label class="payment-option">
                <input type="radio" name="paymentMethod" value="credit" onchange="togglePaymentFields()">
                <span class="material-icons">schedule</span>
                آجل (دين)
              </label>
            </div>
          </div>

          <div class="cash-payment" id="cashPayment">
            <label>المبلغ المدفوع:</label>
            <input type="number" id="paidAmount" value="${currentInvoice.total}" min="0">
            <div class="change-amount">
              <span>المبلغ المتبقي: </span>
              <span id="changeAmount">0.00 د.ع</span>
            </div>
          </div>

          <div class="credit-payment" id="creditPayment" style="display: none;">
            <div class="credit-warning">
              <span class="material-icons">warning</span>
              <p>سيتم تسجيل المبلغ المتبقي كدين على العميل المحدد</p>
            </div>

            <div class="partial-payment-option">
              <label class="checkbox-label">
                <input type="checkbox" id="partialPaymentCheck" onchange="togglePartialPayment()">
                <span class="checkmark"></span>
                دفع جزئي
              </label>
            </div>

            <div class="partial-payment-fields" id="partialPaymentFields" style="display: none;">
              <label>المبلغ المدفوع الآن:</label>
              <input type="number" id="partialAmount" min="0" max="${currentInvoice.total}" onchange="calculatePartialDebt()">
              <div class="debt-calculation">
                <div class="debt-row">
                  <span>المجموع الكلي:</span>
                  <span>${formatPrice(currentInvoice.total)}</span>
                </div>
                <div class="debt-row">
                  <span>المبلغ المدفوع:</span>
                  <span id="paidPartialAmount">0.00 د.ع</span>
                </div>
                <div class="debt-row debt-remaining">
                  <span>المبلغ المتبقي (دين):</span>
                  <span id="remainingDebt">${formatPrice(currentInvoice.total)}</span>
                </div>
              </div>
            </div>

            <div class="customer-validation" id="customerValidation">
              <p>يرجى التأكد من اختيار العميل الصحيح قبل المتابعة</p>
            </div>
          </div>
        </div>

        <div class="payment-modal-footer">
          <button type="button" class="btn btn-outline" onclick="closePaymentModal()">
            إلغاء
          </button>
          <button type="button" class="btn btn-success" onclick="completePayment()">
            <span class="material-icons">check</span>
            إتمام الدفع
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // إضافة تأثير الفتح
  const modal = document.getElementById('paymentModal');
  const container = modal.querySelector('.payment-modal-container');

  modal.style.opacity = '0';
  container.style.transform = 'scale(0.9)';

  setTimeout(() => {
    modal.style.opacity = '1';
    container.style.transform = 'scale(1)';
  }, 10);

  // إضافة مستمع لحساب الباقي
  const paidAmountInput = document.getElementById('paidAmount');
  if (paidAmountInput) {
    paidAmountInput.addEventListener('input', calculateChange);
  }

  // إضافة أنماط CSS للنافذة
  addPaymentModalStyles();
}

function calculateChange() {
  const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
  const change = paidAmount - currentInvoice.total;
  const changeElement = document.getElementById('changeAmount');

  if (changeElement) {
    changeElement.textContent = formatPrice(Math.max(0, change));
    changeElement.style.color = change >= 0 ? '#28a745' : '#dc3545';
  }
}

function closePaymentModal() {
  const modal = document.getElementById('paymentModal');
  if (modal) {
    // إضافة تأثير الإغلاق
    modal.style.opacity = '0';
    modal.querySelector('.payment-modal-container').style.transform = 'scale(0.9)';

    setTimeout(() => {
      modal.remove();
    }, 200);
  }
}

function togglePaymentFields() {
  const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
  const cashPayment = document.getElementById('cashPayment');
  const creditPayment = document.getElementById('creditPayment');

  if (paymentMethod === 'cash') {
    cashPayment.style.display = 'block';
    creditPayment.style.display = 'none';
  } else if (paymentMethod === 'credit') {
    cashPayment.style.display = 'none';
    creditPayment.style.display = 'block';
    // إعادة تعيين الدفع الجزئي
    resetPartialPayment();
  }
}

// تفعيل/إلغاء الدفع الجزئي
function togglePartialPayment() {
  const isChecked = document.getElementById('partialPaymentCheck').checked;
  const partialFields = document.getElementById('partialPaymentFields');

  if (isChecked) {
    partialFields.style.display = 'block';
    document.getElementById('partialAmount').value = '';
    calculatePartialDebt();
  } else {
    partialFields.style.display = 'none';
    resetPartialPayment();
  }
}

// حساب الدين المتبقي للدفع الجزئي
function calculatePartialDebt() {
  const partialAmount = parseFloat(document.getElementById('partialAmount').value) || 0;
  const total = currentInvoice.total;
  const remaining = total - partialAmount;

  document.getElementById('paidPartialAmount').textContent = formatPrice(partialAmount);
  document.getElementById('remainingDebt').textContent = formatPrice(Math.max(0, remaining));

  // تغيير لون المبلغ المتبقي
  const remainingElement = document.getElementById('remainingDebt');
  if (remaining <= 0) {
    remainingElement.style.color = '#28a745';
  } else {
    remainingElement.style.color = '#dc3545';
  }
}

// إعادة تعيين الدفع الجزئي
function resetPartialPayment() {
  const partialCheck = document.getElementById('partialPaymentCheck');
  const partialFields = document.getElementById('partialPaymentFields');

  if (partialCheck) partialCheck.checked = false;
  if (partialFields) partialFields.style.display = 'none';
}

function completePayment() {
  const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;

  if (paymentMethod === 'cash') {
    return completeCashPayment();
  } else if (paymentMethod === 'credit') {
    return completeCreditPayment();
  }
}

function completeCashPayment() {
  const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;

  if (paidAmount < currentInvoice.total) {
    showNotification('المبلغ المدفوع أقل من المطلوب', 'error');
    return;
  }

  // إتمام عملية الدفع النقدي
  const invoiceNumber = generateInvoiceNumber();
  const change = paidAmount - currentInvoice.total;

  // حفظ الفاتورة في سجل المبيعات النقدية
  saveCashInvoice(invoiceNumber, currentInvoice, paidAmount);

  // إظهار تفاصيل الدفع
  showPaymentSuccess({
    type: 'cash',
    invoiceNumber,
    customerName: currentInvoice.customer ? getCustomerById(currentInvoice.customer).fullName : 'عميل عادي',
    total: currentInvoice.total,
    paid: paidAmount,
    change: change
  });

  closePaymentModal();

  // عدم مسح الفاتورة - سيتم حفظها في بطاقة العميل
  // clearInvoice();
}

function completeCreditPayment() {
  const invoiceNotes = document.getElementById('invoiceNotes')?.value || '';

  // التحقق من اختيار العميل
  if (!currentInvoice.customer) {
    showNotification('يجب اختيار العميل للدفع الآجل', 'error');
    return;
  }

  // التحقق من صحة بيانات العميل
  const customer = getCustomerById(currentInvoice.customer);
  if (!customer) {
    showNotification('العميل المحدد غير موجود', 'error');
    return;
  }

  // التحقق من اكتمال بيانات العميل
  if (!customer.fullName || !customer.idNumber || !customer.phone || !customer.region) {
    showNotification('بيانات العميل غير مكتملة. يرجى تحديث بيانات العميل أولاً', 'error');
    return;
  }

  // التحقق من الدفع الجزئي
  const isPartialPayment = document.getElementById('partialPaymentCheck')?.checked || false;
  let paidAmount = 0;
  let remainingAmount = currentInvoice.total;

  if (isPartialPayment) {
    paidAmount = parseFloat(document.getElementById('partialAmount').value) || 0;

    if (paidAmount < 0 || paidAmount > currentInvoice.total) {
      showNotification('مبلغ الدفع الجزئي غير صحيح', 'error');
      return;
    }

    remainingAmount = currentInvoice.total - paidAmount;
  }

  // إتمام عملية الدفع الآجل
  const invoiceNumber = generateInvoiceNumber();

  // حفظ الفاتورة والدين في سجل العميل مع الملاحظات
  saveCreditInvoice(invoiceNumber, currentInvoice, customer, invoiceNotes, paidAmount, remainingAmount);

  // إظهار تفاصيل الدفع
  showPaymentSuccess({
    type: isPartialPayment ? 'partial' : 'credit',
    invoiceNumber,
    customerName: customer.fullName,
    total: currentInvoice.total,
    paidAmount: paidAmount,
    creditAmount: remainingAmount,
    notes: invoiceNotes,
    isPartial: isPartialPayment
  });

  closePaymentModal();

  // عدم مسح الفاتورة - سيتم حفظها في بطاقة العميل
  // clearInvoice();
}

// حفظ فاتورة نقدية
async function saveCashInvoice(invoiceNumber, invoice, paidAmount, notes = '') {
  const customer = getCustomerById(invoice.customer);
  const invoiceData = {
    invoiceNumber: invoiceNumber,
    customerId: invoice.customer,
    customerName: customer ? customer.full_name : 'عميل عادي',
    total: invoice.total,
    paidAmount: paidAmount,
    remainingAmount: 0,
    status: 'paid',
    paymentMethod: 'cash',
    notes: notes,
    items: invoice.items.map(item => ({
      productId: item.id,
      productName: item.name,
      quantity: item.quantity,
      price: item.price,
      total: item.total
    }))
  };

  try {
    if (window.electronAPI) {
      await window.electronAPI.saveInvoice(invoiceData);
      console.log('تم حفظ الفاتورة النقدية في قاعدة البيانات');
    } else {
      // حفظ محلي كـ fallback
      saveInvoiceToLocalStorage(invoiceData);
      if (invoice.customer) {
        addInvoiceToCustomer(invoice.customer, invoiceData);
      }
      console.log('تم حفظ فاتورة نقدية محلياً:', invoiceData);
    }
  } catch (error) {
    console.error('خطأ في حفظ الفاتورة:', error);
    showNotification('خطأ في حفظ الفاتورة', 'error');
  }
}

// حفظ فاتورة آجلة
async function saveCreditInvoice(invoiceNumber, invoice, customer, notes = '', paidAmount = 0, remainingAmount = null) {
  if (remainingAmount === null) {
    remainingAmount = invoice.total - paidAmount;
  }

  let status = 'pending';
  if (paidAmount > 0 && remainingAmount > 0) {
    status = 'partial';
  } else if (remainingAmount <= 0) {
    status = 'paid';
  }

  const invoiceData = {
    invoiceNumber: invoiceNumber,
    customerId: customer.id,
    customerName: customer.full_name || customer.fullName,
    total: invoice.total,
    paidAmount: paidAmount,
    remainingAmount: remainingAmount,
    status: status,
    paymentMethod: paidAmount > 0 ? 'partial' : 'credit',
    notes: notes,
    items: invoice.items.map(item => ({
      productId: item.id,
      productName: item.name,
      quantity: item.quantity,
      price: item.price,
      total: item.total
    }))
  };

  try {
    if (window.electronAPI) {
      await window.electronAPI.saveInvoice(invoiceData);
      console.log('تم حفظ الفاتورة الآجلة في قاعدة البيانات');
    } else {
      // حفظ محلي كـ fallback
      saveInvoiceToLocalStorage(invoiceData);
      addInvoiceToCustomer(customer.id, invoiceData);
      updateCustomerDebt(customer.id, remainingAmount);
      console.log('تم حفظ فاتورة آجلة محلياً:', invoiceData);
    }
  } catch (error) {
    console.error('خطأ في حفظ الفاتورة:', error);
    showNotification('خطأ في حفظ الفاتورة', 'error');
  }
}

// حفظ الفاتورة في التخزين المحلي
function saveInvoiceToLocalStorage(invoiceData) {
  let invoices = JSON.parse(localStorage.getItem('invoices') || '[]');
  invoices.push(invoiceData);
  localStorage.setItem('invoices', JSON.stringify(invoices));
}

// إضافة الفاتورة لسجل العميل
function addInvoiceToCustomer(customerId, invoiceData) {
  // تحديث بيانات العميل في customers.js
  if (window.customersList) {
    const customerIndex = window.customersList.findIndex(c => c.id === customerId);
    if (customerIndex !== -1) {
      if (!window.customersList[customerIndex].invoices) {
        window.customersList[customerIndex].invoices = [];
      }
      window.customersList[customerIndex].invoices.push(invoiceData);

      // تحديث عدد المشتريات
      window.customersList[customerIndex].totalPurchases =
        (window.customersList[customerIndex].totalPurchases || 0) + 1;
    }
  }
}

// تحديث دين العميل
function updateCustomerDebt(customerId, amount) {
  if (window.customersList) {
    const customerIndex = window.customersList.findIndex(c => c.id === customerId);
    if (customerIndex !== -1) {
      window.customersList[customerIndex].totalDebt =
        (window.customersList[customerIndex].totalDebt || 0) + amount;
    }
  }
}

// الحصول على بيانات العميل
function getCustomerById(customerId) {
  return availableCustomers.find(customer => customer.id === customerId);
}

// إظهار نافذة نجاح الدفع
function showPaymentSuccess(paymentData) {
  const modalHTML = `
    <div id="paymentSuccessModal" class="modal-overlay show">
      <div class="modal-container success-modal">
        <div class="modal-header success-header">
          <div class="success-icon">
            <span class="material-icons">check_circle</span>
          </div>
          <h3>تم إتمام العملية بنجاح</h3>
        </div>

        <div class="modal-body">
          <div class="payment-details">
            <div class="invoice-info">
              <h4>تفاصيل الفاتورة</h4>
              <div class="detail-row">
                <span class="label">رقم الفاتورة:</span>
                <span class="value">${paymentData.invoiceNumber}</span>
              </div>
              <div class="detail-row">
                <span class="label">العميل:</span>
                <span class="value">${paymentData.customerName}</span>
              </div>
              <div class="detail-row">
                <span class="label">المجموع الكلي:</span>
                <span class="value total-amount">${formatPrice(paymentData.total)}</span>
              </div>
              ${paymentData.notes ? `
                <div class="detail-row">
                  <span class="label">ملاحظات:</span>
                  <span class="value invoice-notes">${paymentData.notes}</span>
                </div>
              ` : ''}
            </div>

            ${paymentData.type === 'cash' ? `
              <div class="cash-details">
                <h4>تفاصيل الدفع النقدي</h4>
                <div class="detail-row">
                  <span class="label">المبلغ المدفوع:</span>
                  <span class="value">${formatPrice(paymentData.paid)}</span>
                </div>
                <div class="detail-row">
                  <span class="label">المبلغ المتبقي:</span>
                  <span class="value change-amount">${formatPrice(paymentData.change)}</span>
                </div>
                <div class="payment-status cash-status">
                  <span class="material-icons">payments</span>
                  <span>تم الدفع نقداً - سُجلت الفاتورة في سجل المبيعات</span>
                </div>
              </div>
            ` : paymentData.type === 'partial' ? `
              <div class="partial-details">
                <h4>تفاصيل الدفع الجزئي</h4>
                <div class="detail-row">
                  <span class="label">المبلغ المدفوع:</span>
                  <span class="value paid-amount">${formatPrice(paymentData.paidAmount)}</span>
                </div>
                <div class="detail-row">
                  <span class="label">المبلغ المتبقي (دين):</span>
                  <span class="value debt-amount">${formatPrice(paymentData.creditAmount)}</span>
                </div>
                <div class="payment-status partial-status">
                  <span class="material-icons">account_balance_wallet</span>
                  <span>تم الدفع الجزئي - المبلغ المتبقي مُسجل كدين</span>
                </div>
              </div>
            ` : `
              <div class="credit-details">
                <h4>تفاصيل الدفع الآجل</h4>
                <div class="detail-row">
                  <span class="label">مبلغ الدين:</span>
                  <span class="value debt-amount">${formatPrice(paymentData.creditAmount)}</span>
                </div>
                <div class="payment-status credit-status">
                  <span class="material-icons">schedule</span>
                  <span>تم تسجيل الدين في بطاقة العميل</span>
                </div>
              </div>
            `}

            <div class="invoice-summary">
              <h4>ملخص المنتجات</h4>
              <div class="products-list">
                ${currentInvoice.items.map(item => `
                  <div class="product-summary">
                    <span class="product-name">${item.name}</span>
                    <span class="product-qty">×${item.quantity}</span>
                    <span class="product-total">${formatPrice(item.total)}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="printInvoice('${paymentData.invoiceNumber}')">
            <span class="material-icons">print</span>
            طباعة الفاتورة
          </button>
          <button type="button" class="btn btn-primary" onclick="closePaymentSuccessModal()">
            <span class="material-icons">done</span>
            موافق
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  addPaymentSuccessStyles();
}

function closePaymentSuccessModal() {
  const modal = document.getElementById('paymentSuccessModal');
  if (modal) {
    modal.remove();
  }

  // مسح الفاتورة تلقائياً بدون إشعار
  clearInvoiceQuietly();
}

// مسح الفاتورة بدون إشعارات
function clearInvoiceQuietly() {
  currentInvoice = {
    items: [],
    customer: null,
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0
  };

  // إعادة تعيين واجهة العميل
  const customerSelect = document.getElementById('customerSelect');
  const selectedCustomerDiv = document.getElementById('selectedCustomer');

  if (customerSelect) customerSelect.value = '';
  if (selectedCustomerDiv) selectedCustomerDiv.style.display = 'none';
  if (customerSelect) customerSelect.style.display = 'block';

  document.getElementById('discount').value = '0';

  updateInvoiceDisplay();
  calculateTotal();
}

// إضافة أنماط CSS لنافذة الدفع المنبثقة
function addPaymentModalStyles() {
  const styles = `
    <style id="paymentModalStyles">
      .payment-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(2px);
        transition: opacity 0.3s ease;
      }

      .payment-modal-container {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 85vh;
        overflow-y: auto;
        transform: scale(1);
        transition: all 0.3s ease;
      }

      .payment-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 28px;
        border-bottom: 1px solid #e9ecef;
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 16px 16px 0 0;
      }

      .payment-modal-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }

      .payment-modal-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s ease;
      }

      .payment-modal-close:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .payment-modal-body {
        padding: 28px;
      }

      .payment-modal-footer {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        padding: 20px 28px;
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;
        border-radius: 0 0 16px 16px;
      }

      .payment-options {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
      }

      .payment-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .payment-option:hover {
        border-color: #007bff;
        background: #f8f9fa;
      }

      .payment-option input[type="radio"] {
        margin: 0;
      }

      .payment-option input[type="radio"]:checked + .material-icons {
        color: #007bff;
      }

      .payment-option:has(input[type="radio"]:checked) {
        border-color: #007bff;
        background: #e3f2fd;
      }

      .credit-warning {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        margin-bottom: 12px;
      }

      .credit-warning .material-icons {
        color: #856404;
      }

      .credit-warning p {
        margin: 0;
        color: #856404;
        font-size: 14px;
      }

      .customer-validation {
        padding: 12px;
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 6px;
      }

      .customer-validation p {
        margin: 0;
        color: #0c5460;
        font-size: 14px;
      }

      .partial-payment-option {
        margin: 16px 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
      }

      .checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-weight: 500;
        color: #495057;
      }

      .checkbox-label input[type="checkbox"] {
        margin: 0;
        transform: scale(1.2);
      }

      .partial-payment-fields {
        margin-top: 16px;
        padding: 16px;
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
      }

      .partial-payment-fields label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
      }

      .partial-payment-fields input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .debt-calculation {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
      }

      .debt-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        font-size: 14px;
      }

      .debt-row.debt-remaining {
        border-top: 1px solid #dee2e6;
        margin-top: 8px;
        padding-top: 8px;
        font-weight: 600;
        color: #dc3545;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}

// إضافة أنماط CSS لنافذة نجاح الدفع
function addPaymentSuccessStyles() {
  const styles = `
    <style id="paymentSuccessStyles">
      .success-modal {
        max-width: 500px;
      }

      .success-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        text-align: center;
        padding: 24px;
        border-radius: 12px 12px 0 0;
      }

      .success-icon {
        margin-bottom: 12px;
      }

      .success-icon .material-icons {
        font-size: 48px;
        color: white;
      }

      .success-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }

      .payment-details {
        padding: 20px 0;
      }

      .payment-details h4 {
        margin: 0 0 12px 0;
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 8px;
      }

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
      }

      .detail-row .label {
        font-weight: 500;
        color: #6c757d;
      }

      .detail-row .value {
        font-weight: 600;
        color: #2c3e50;
      }

      .total-amount {
        color: #28a745 !important;
        font-size: 18px !important;
      }

      .change-amount {
        color: #007bff !important;
        font-size: 16px !important;
      }

      .debt-amount {
        color: #dc3545 !important;
        font-size: 16px !important;
      }

      .payment-status {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border-radius: 8px;
        margin-top: 12px;
        font-weight: 500;
      }

      .cash-status {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .credit-status {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      .invoice-summary {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #e9ecef;
      }

      .products-list {
        max-height: 150px;
        overflow-y: auto;
      }

      .product-summary {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 8px;
        padding: 6px 0;
        border-bottom: 1px solid #f8f9fa;
        align-items: center;
      }

      .product-name {
        font-size: 13px;
        color: #2c3e50;
      }

      .product-qty {
        font-size: 12px;
        color: #6c757d;
        text-align: center;
      }

      .product-total {
        font-size: 13px;
        font-weight: 600;
        color: #28a745;
        text-align: left;
      }

      .cash-details, .credit-details, .partial-details {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e9ecef;
      }

      .partial-status {
        background: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
      }

      .paid-amount {
        color: #28a745 !important;
        font-size: 16px !important;
      }

      .invoice-notes {
        color: #6c757d !important;
        font-style: italic;
        font-size: 14px !important;
        background: #f8f9fa;
        padding: 8px 12px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        max-width: 200px;
        word-wrap: break-word;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}

// ===== وظائف مساعدة =====
function formatPrice(price) {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price) + ' د.ع';
}

function generateInvoiceNumber() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-4);

  return `INV-${year}${month}${day}-${time}`;
}

function scanBarcode() {
  showNotification('ميزة مسح الباركود قيد التطوير', 'info');
}

// قائمة العملاء المتاحة
const availableCustomers = [
  {
    id: '1',
    fullName: 'محمد أحمد حسين',
    idNumber: '123456789012',
    phone: '07901234567',
    region: 'بغداد'
  },
  {
    id: '2',
    fullName: 'علي عبدالله كريم',
    idNumber: '987654321098',
    phone: '07801234567',
    region: 'البصرة'
  },
  {
    id: '3',
    fullName: 'شركة البناء العراقية',
    idNumber: '112233445566',
    phone: '07701234567',
    region: 'أربيل'
  }
];

function searchCustomers() {
  const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
  const customerSelect = document.getElementById('customerSelect');
  const clearBtn = document.querySelector('.clear-search');

  if (searchTerm.length > 0) {
    clearBtn.style.display = 'flex';

    // فلترة العملاء
    const filteredCustomers = availableCustomers.filter(customer =>
      customer.fullName.toLowerCase().includes(searchTerm) ||
      customer.phone.includes(searchTerm) ||
      customer.region.toLowerCase().includes(searchTerm)
    );

    // تحديث القائمة
    customerSelect.innerHTML = '<option value="">عميل عادي</option>';
    filteredCustomers.forEach(customer => {
      const option = document.createElement('option');
      option.value = customer.id;
      option.textContent = `${customer.fullName} - ${customer.phone}`;
      customerSelect.appendChild(option);
    });

    if (filteredCustomers.length === 0) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'لا توجد نتائج';
      option.disabled = true;
      customerSelect.appendChild(option);
    }
  } else {
    clearBtn.style.display = 'none';
    resetCustomerList();
  }
}

function clearCustomerSearch() {
  document.getElementById('customerSearch').value = '';
  document.querySelector('.clear-search').style.display = 'none';
  resetCustomerList();
}

function resetCustomerList() {
  const customerSelect = document.getElementById('customerSelect');
  customerSelect.innerHTML = '<option value="">عميل عادي</option>';
  availableCustomers.forEach(customer => {
    const option = document.createElement('option');
    option.value = customer.id;
    option.textContent = `${customer.fullName} - ${customer.phone}`;
    customerSelect.appendChild(option);
  });
}

function selectCustomer() {
  const customerSelect = document.getElementById('customerSelect');
  const customerId = customerSelect.value;
  const selectedCustomerDiv = document.getElementById('selectedCustomer');

  if (customerId) {
    const customer = availableCustomers.find(c => c.id === customerId);
    if (customer) {
      currentInvoice.customer = customerId;

      // إظهار تفاصيل العميل المختار
      selectedCustomerDiv.style.display = 'flex';
      selectedCustomerDiv.querySelector('.customer-name').textContent = customer.fullName;
      selectedCustomerDiv.querySelector('.customer-phone').textContent = customer.phone;

      // إخفاء قائمة الاختيار
      customerSelect.style.display = 'none';

      showNotification(`تم اختيار العميل: ${customer.fullName}`, 'success');
    }
  } else {
    currentInvoice.customer = null;
    selectedCustomerDiv.style.display = 'none';
    customerSelect.style.display = 'block';
  }
}

function clearSelectedCustomer() {
  currentInvoice.customer = null;
  document.getElementById('selectedCustomer').style.display = 'none';
  document.getElementById('customerSelect').style.display = 'block';
  document.getElementById('customerSelect').value = '';
  showNotification('تم إلغاء اختيار العميل', 'info');
}

function addNewCustomer() {
  // إنشاء نافذة إضافة عميل جديد
  createCustomerModal();
}

function createCustomerModal() {
  // إزالة أي نافذة موجودة
  const existingModal = document.getElementById('customerModal');
  if (existingModal) {
    existingModal.remove();
  }

  const modalHTML = `
    <div id="customerModal" class="modal-overlay show">
      <div class="modal-container">
        <div class="modal-header">
          <h3>إضافة عميل جديد</h3>
          <button class="modal-close" onclick="closeCustomerModal()">
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="modal-body">
          <form id="customerForm">
            <div class="form-group">
              <label class="form-label">الاسم الثلاثي *</label>
              <input type="text" name="fullName" class="form-control" placeholder="الاسم الأول الأوسط الأخير" required>
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهوية الوطنية العراقية *</label>
              <input type="text" name="idNumber" class="form-control" placeholder="رقم الهوية الوطنية العراقية (12 رقم)" required maxlength="12">
            </div>

            <div class="form-group">
              <label class="form-label">رقم الهاتف *</label>
              <input type="tel" name="phone" class="form-control" placeholder="07xxxxxxxxx" required>
            </div>

            <div class="form-group">
              <label class="form-label">المحافظة *</label>
              <select name="region" class="form-control" required>
                <option value="">اختر المحافظة</option>
                <option value="بغداد">بغداد</option>
                <option value="البصرة">البصرة</option>
                <option value="نينوى">نينوى</option>
                <option value="أربيل">أربيل</option>
                <option value="النجف">النجف</option>
                <option value="كربلاء">كربلاء</option>
                <option value="بابل">بابل</option>
                <option value="ديالى">ديالى</option>
                <option value="الأنبار">الأنبار</option>
                <option value="كركوك">كركوك</option>
                <option value="صلاح الدين">صلاح الدين</option>
                <option value="واسط">واسط</option>
                <option value="ذي قار">ذي قار</option>
                <option value="المثنى">المثنى</option>
                <option value="القادسية">القادسية</option>
                <option value="ميسان">ميسان</option>
                <option value="دهوك">دهوك</option>
                <option value="السليمانية">السليمانية</option>
                <option value="حلبجة">حلبجة</option>
              </select>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline" onclick="closeCustomerModal()">
            إلغاء
          </button>
          <button type="button" class="btn btn-primary" onclick="saveNewCustomer()">
            <span class="material-icons">save</span>
            حفظ العميل
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  addCustomerModalStyles();
}

function closeCustomerModal() {
  const modal = document.getElementById('customerModal');
  if (modal) {
    modal.remove();
  }
}

function saveNewCustomer() {
  const form = document.getElementById('customerForm');
  if (!form) return;

  const formData = new FormData(form);
  const customerData = {
    id: Date.now().toString(),
    fullName: formData.get('fullName'),
    idNumber: formData.get('idNumber'),
    phone: formData.get('phone'),
    region: formData.get('region')
  };

  // التحقق من البيانات المطلوبة
  if (!customerData.fullName || !customerData.idNumber || !customerData.phone || !customerData.region) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // التحقق من صحة رقم الهاتف العراقي
  const phoneRegex = /^07\d{9}$/;
  if (!phoneRegex.test(customerData.phone)) {
    showNotification('يرجى إدخال رقم هاتف عراقي صحيح (07xxxxxxxxx)', 'error');
    return;
  }

  // التحقق من صحة رقم الهوية العراقية
  const idRegex = /^\d{12}$/;
  if (!idRegex.test(customerData.idNumber)) {
    showNotification('يرجى إدخال رقم هوية وطنية عراقية صحيح (12 رقم)', 'error');
    return;
  }

  // إضافة العميل إلى القائمة
  availableCustomers.push(customerData);

  // تحديث قائمة العملاء
  resetCustomerList();

  // اختيار العميل الجديد تلقائياً
  document.getElementById('customerSelect').value = customerData.id;
  selectCustomer();

  closeCustomerModal();
  showNotification(`تم إضافة العميل: ${customerData.fullName}`, 'success');
}

function addCustomerModalStyles() {
  const styles = `
    <style id="customerModalStyles">
      .form-group {
        margin-bottom: 16px;
      }

      .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #2c3e50;
        font-size: 14px;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s ease;
      }

      .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }

      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      }

      .modal-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #e9ecef;
      }

      .modal-header h3 {
        margin: 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }

      .modal-close {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .modal-close:hover {
        background: #f8f9fa;
        color: #495057;
      }

      .modal-body {
        padding: 24px;
      }

      .modal-footer {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        padding: 20px 24px;
        border-top: 1px solid #e9ecef;
      }

      .btn-primary {
        background: #007bff;
        color: white;
        border: 1px solid #007bff;
      }

      .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
      }
    </style>
  `;

  document.head.insertAdjacentHTML('beforeend', styles);
}

function printInvoice(invoiceNumber) {
  // طباعة الفاتورة (يمكن تطويرها لاحقاً)
  showNotification(`جاري طباعة الفاتورة ${invoiceNumber}`, 'info');
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-weight: 500;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// ===== تهيئة الصفحة =====
document.addEventListener('DOMContentLoaded', initPOS);

// ===== تصدير الوظائف =====
window.searchProducts = searchProducts;
window.filterByCategory = filterByCategory;
window.addToInvoice = addToInvoice;
window.removeFromInvoice = removeFromInvoice;
window.updateQuantity = updateQuantity;
window.calculateTotal = calculateTotal;
window.clearInvoice = clearInvoice;
window.holdInvoice = holdInvoice;
window.processPayment = processPayment;
window.selectCustomer = selectCustomer;
window.addNewCustomer = addNewCustomer;
window.scanBarcode = scanBarcode;
window.closePaymentModal = closePaymentModal;
window.togglePartialPayment = togglePartialPayment;
window.calculatePartialDebt = calculatePartialDebt;
window.togglePaymentFields = togglePaymentFields;
window.completePayment = completePayment;
window.completeCashPayment = completeCashPayment;
window.completeCreditPayment = completeCreditPayment;
window.closePaymentSuccessModal = closePaymentSuccessModal;
window.searchCustomers = searchCustomers;
window.clearCustomerSearch = clearCustomerSearch;
window.clearSelectedCustomer = clearSelectedCustomer;
window.closeCustomerModal = closeCustomerModal;
window.saveNewCustomer = saveNewCustomer;
window.closePaymentSuccessModal = closePaymentSuccessModal;
