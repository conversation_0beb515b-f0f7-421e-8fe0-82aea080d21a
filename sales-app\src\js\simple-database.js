// ===== قاعدة بيانات وهمية مبسطة =====

class SimpleSalesDatabase {
  constructor() {
    this.data = {
      categories: [],
      products: [],
      customers: [],
      invoices: [],
      payments: [],
      customerInvoices: {},
      users: []
    };
    this.init();
  }

  // ===== تهيئة قاعدة البيانات =====
  init() {
    try {
      console.log('✅ تم تهيئة قاعدة البيانات الوهمية بنجاح');
      this.insertDefaultData();
    } catch (error) {
      console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    }
  }

  // ===== إدراج البيانات الافتراضية =====
  insertDefaultData() {
    // إدراج الفئات
    this.data.categories = [
      { id: 1, name: 'أدوات كهربائية', description: 'جميع الأدوات الكهربائية', icon: 'electrical_services' },
      { id: 2, name: 'مواد بناء', description: 'مواد البناء والإنشاء', icon: 'construction' },
      { id: 3, name: 'سباكة', description: 'أدوات ومواد السباكة', icon: 'plumbing' },
      { id: 4, name: 'دهانات', description: 'الدهانات والألوان', icon: 'format_paint' },
      { id: 5, name: 'أدوات يدوية', description: 'الأدوات اليدوية', icon: 'build' },
      { id: 6, name: 'إضاءة', description: 'أدوات الإضاءة', icon: 'lightbulb' }
    ];

    // إدراج المنتجات
    this.data.products = [
      {
        id: 1,
        name: 'كابل كهربائي 2.5 مم',
        code: 'ELEC001',
        category_id: 1,
        description: 'كابل كهربائي عالي الجودة',
        purchase_price: 20,
        sale_price: 25,
        stock_quantity: 150,
        min_stock: 10,
        max_stock: 500,
        unit: 'متر',
        barcode: '1234567890123',
        status: 'active'
      },
      {
        id: 2,
        name: 'أسمنت بورتلاندي',
        code: 'BUILD001',
        category_id: 2,
        description: 'أسمنت بورتلاندي عالي الجودة',
        purchase_price: 25,
        sale_price: 28,
        stock_quantity: 8,
        min_stock: 5,
        max_stock: 100,
        unit: 'كيس',
        barcode: '1234567890124',
        status: 'active'
      },
      {
        id: 3,
        name: 'أنبوب PVC 4 بوصة',
        code: 'PLUMB001',
        category_id: 3,
        description: 'أنبوب PVC للصرف الصحي',
        purchase_price: 40,
        sale_price: 45,
        stock_quantity: 45,
        min_stock: 10,
        max_stock: 200,
        unit: 'قطعة',
        barcode: '1234567890125',
        status: 'active'
      }
    ];

    // إدراج العملاء
    this.data.customers = [
      {
        id: 1,
        name: 'أحمد محمد',
        phone: '0501234567',
        email: '<EMAIL>',
        address: 'شارع الملك فهد، الرياض',
        city: 'الرياض',
        customer_type: 'مميز',
        tax_number: '',
        credit_limit: 10000,
        current_balance: 0,
        status: 'active',
        notes: 'عميل مميز'
      },
      {
        id: 2,
        name: 'سعد الأحمد',
        phone: '0509876543',
        email: '<EMAIL>',
        address: 'شارع التحلية، جدة',
        city: 'جدة',
        customer_type: 'عادي',
        tax_number: '',
        credit_limit: 5000,
        current_balance: 0,
        status: 'active',
        notes: ''
      },
      {
        id: 3,
        name: 'عبدالله محمد',
        phone: '0551122334',
        email: '<EMAIL>',
        address: 'شارع الظهران، الدمام',
        city: 'الدمام',
        customer_type: 'تجاري',
        tax_number: '123456789012345',
        credit_limit: 50000,
        current_balance: 0,
        status: 'active',
        notes: 'شركة مقاولات'
      }
    ];

    // إدراج المستخدمين
    this.data.users = [
      {
        id: 1,
        username: 'admin',
        password: 'admin123',
        full_name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'admin',
        permissions: JSON.stringify(['all']),
        status: 'active'
      }
    ];

    console.log('📊 تم إدراج البيانات الافتراضية');
  }

  // ===== وظائف الفئات =====
  getAllCategories() {
    return Promise.resolve(this.data.categories);
  }

  addCategory(categoryData) {
    const newId = Math.max(...this.data.categories.map(c => c.id), 0) + 1;
    const category = { id: newId, ...categoryData };
    this.data.categories.push(category);
    return Promise.resolve(category);
  }

  updateCategory(id, categoryData) {
    const index = this.data.categories.findIndex(c => c.id === id);
    if (index !== -1) {
      this.data.categories[index] = { ...this.data.categories[index], ...categoryData };
      return Promise.resolve(this.data.categories[index]);
    }
    return Promise.reject(new Error('Category not found'));
  }

  deleteCategory(id) {
    const index = this.data.categories.findIndex(c => c.id === id);
    if (index !== -1) {
      this.data.categories.splice(index, 1);
      return Promise.resolve(true);
    }
    return Promise.reject(new Error('Category not found'));
  }

  // ===== وظائف المنتجات =====
  getAllProducts() {
    return Promise.resolve(this.data.products);
  }

  getProductById(id) {
    const product = this.data.products.find(p => p.id === id);
    return Promise.resolve(product);
  }

  addProduct(productData) {
    const newId = Math.max(...this.data.products.map(p => p.id), 0) + 1;
    const product = { id: newId, ...productData };
    this.data.products.push(product);
    return Promise.resolve(product);
  }

  updateProduct(id, productData) {
    const index = this.data.products.findIndex(p => p.id === id);
    if (index !== -1) {
      this.data.products[index] = { ...this.data.products[index], ...productData };
      return Promise.resolve(this.data.products[index]);
    }
    return Promise.reject(new Error('Product not found'));
  }

  deleteProduct(id) {
    const index = this.data.products.findIndex(p => p.id === id);
    if (index !== -1) {
      this.data.products.splice(index, 1);
      return Promise.resolve(true);
    }
    return Promise.reject(new Error('Product not found'));
  }

  // ===== وظائف العملاء =====
  getAllCustomers() {
    return Promise.resolve(this.data.customers);
  }

  getCustomerById(id) {
    const customer = this.data.customers.find(c => c.id === id);
    return Promise.resolve(customer);
  }

  addCustomer(customerData) {
    const newId = Math.max(...this.data.customers.map(c => c.id), 0) + 1;
    const customer = { id: newId, ...customerData };
    this.data.customers.push(customer);
    return Promise.resolve(customer);
  }

  updateCustomer(id, customerData) {
    const index = this.data.customers.findIndex(c => c.id === id);
    if (index !== -1) {
      this.data.customers[index] = { ...this.data.customers[index], ...customerData };
      return Promise.resolve(this.data.customers[index]);
    }
    return Promise.reject(new Error('Customer not found'));
  }

  deleteCustomer(id) {
    const index = this.data.customers.findIndex(c => c.id === id);
    if (index !== -1) {
      this.data.customers.splice(index, 1);
      return Promise.resolve(true);
    }
    return Promise.reject(new Error('Customer not found'));
  }

  // ===== وظائف الفواتير =====
  getAllInvoices() {
    return Promise.resolve(this.data.invoices);
  }

  getInvoiceById(id) {
    const invoice = this.data.invoices.find(i => i.id === id);
    return Promise.resolve(invoice);
  }

  getCustomerInvoices(customerId) {
    const invoices = this.data.invoices.filter(i => i.customer_id === customerId);
    return Promise.resolve(invoices);
  }

  addInvoice(invoiceData) {
    const newId = Math.max(...this.data.invoices.map(i => i.id), 0) + 1;
    const invoice = {
      id: newId,
      invoice_number: `INV-${String(newId).padStart(6, '0')}`,
      date: new Date().toISOString(),
      status: 'pending', // pending, partial, paid
      ...invoiceData
    };
    this.data.invoices.push(invoice);

    // إضافة الفاتورة لسجل العميل
    if (!this.data.customerInvoices[invoiceData.customer_id]) {
      this.data.customerInvoices[invoiceData.customer_id] = [];
    }
    this.data.customerInvoices[invoiceData.customer_id].push(invoice.id);

    return Promise.resolve(invoice);
  }

  updateInvoice(id, invoiceData) {
    const index = this.data.invoices.findIndex(i => i.id === id);
    if (index !== -1) {
      this.data.invoices[index] = { ...this.data.invoices[index], ...invoiceData };
      return Promise.resolve(this.data.invoices[index]);
    }
    return Promise.reject(new Error('Invoice not found'));
  }

  // ===== وظائف المدفوعات =====
  getAllPayments() {
    return Promise.resolve(this.data.payments);
  }

  getCustomerPayments(customerId) {
    const payments = this.data.payments.filter(p => p.customer_id === customerId);
    return Promise.resolve(payments);
  }

  addPayment(paymentData) {
    const newId = Math.max(...this.data.payments.map(p => p.id), 0) + 1;
    const payment = {
      id: newId,
      payment_number: `PAY-${String(newId).padStart(6, '0')}`,
      date: new Date().toISOString(),
      ...paymentData
    };
    this.data.payments.push(payment);

    // تحديث حالة الفاتورة إذا كان الدفع مرتبط بفاتورة محددة
    if (paymentData.invoice_id) {
      this.updateInvoicePaymentStatus(paymentData.invoice_id, paymentData.amount);
    }

    return Promise.resolve(payment);
  }

  updateInvoicePaymentStatus(invoiceId, paymentAmount) {
    const invoice = this.data.invoices.find(i => i.id === invoiceId);
    if (invoice) {
      invoice.paid_amount = (invoice.paid_amount || 0) + paymentAmount;
      invoice.remaining_amount = invoice.total - invoice.paid_amount;

      if (invoice.remaining_amount <= 0) {
        invoice.status = 'paid';
        invoice.remaining_amount = 0;
      } else if (invoice.paid_amount > 0) {
        invoice.status = 'partial';
      }
    }
  }

  // ===== وظائف تقارير العملاء =====
  getCustomerFinancialSummary(customerId) {
    const invoices = this.data.invoices.filter(i => i.customer_id === customerId);
    const payments = this.data.payments.filter(p => p.customer_id === customerId);

    const totalInvoices = invoices.length;
    const totalAmount = invoices.reduce((sum, i) => sum + i.total, 0);
    const totalPaid = payments.reduce((sum, p) => sum + p.amount, 0);
    const totalDebt = totalAmount - totalPaid;
    const pendingInvoices = invoices.filter(i => i.status !== 'paid').length;

    return Promise.resolve({
      totalInvoices,
      totalAmount,
      totalPaid,
      totalDebt,
      pendingInvoices,
      invoices: invoices.sort((a, b) => new Date(b.date) - new Date(a.date)),
      payments: payments.sort((a, b) => new Date(b.date) - new Date(a.date))
    });
  }

  // ===== وظائف الإحصائيات =====
  getInventoryStats() {
    const lowStockItems = this.data.products.filter(p => p.stock_quantity <= p.min_stock).length;
    return Promise.resolve({
      totalProducts: this.data.products.length,
      lowStockItems: lowStockItems,
      totalValue: this.data.products.reduce((sum, p) => sum + (p.stock_quantity * p.purchase_price), 0)
    });
  }

  getCustomersStats() {
    return Promise.resolve({
      totalCustomers: this.data.customers.length,
      activeCustomers: this.data.customers.filter(c => c.status === 'active').length
    });
  }

  getSalesStats() {
    return Promise.resolve({
      totalInvoices: this.data.invoices.length,
      totalSales: this.data.invoices.reduce((sum, i) => sum + i.total, 0)
    });
  }
}

module.exports = SimpleSalesDatabase;
