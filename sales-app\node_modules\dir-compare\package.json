{"name": "dir-compare", "version": "3.3.0", "description": "Node JS directory compare", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/gliviu/dir-compare"}, "keywords": ["compare", "directory", "folder"], "files": ["build/src"], "scripts": {"prepublishOnly": "npm run clean && npm run build && npm run lint", "clean": "rm -rf build && rm -rf .nyc_output && rm -rf coverage", "copydeps": "copyfiles \"test/expected/**\" test/testdir.tar \"test/extended/res/**\" build", "build": "tsc && npm run copydeps", "lint": "eslint ./src ./test --ext .ts,.js", "watch": "tsc-watch --onSuccess 'npm run copydeps --silent'", "pretest": "npm install && npm run build", "test": "node build/test/runTests.js", "extest": "npm run pretest && ./test/extended/init.sh && test/extended/runall.sh", "coverage": "npx nyc --exclude \"build/test/**\" --reporter=lcov npm test && npx nyc report", "toc": "npx markdown-toc README.md; echo \n", "docs": "typedoc --includeVersion --excludeExternals --theme minimal --readme none --gitRevision master --toc compare,compareSync,fileCompareHandlers,Options,Result --out docs ./src/index.ts"}, "dependencies": {"buffer-equal": "^1.0.0", "minimatch": "^3.0.4"}, "devDependencies": {"@types/node": "^12.11.7", "@typescript-eslint/eslint-plugin": "^4.14.0", "@typescript-eslint/parser": "^4.14.0", "colors": "^1.4.0", "copyfiles": "^1.2.0", "eslint": "^7.18.0", "memory-streams": "0.1.0", "semver": "5.6.0", "tar-fs": "2.1.1", "temp": "0.9.0", "ts-node": "9.1.1", "tsc-watch": "^4.2.9", "typedoc": "0.20.16", "typescript": "^4.1.3"}, "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/gliviu/dir-compare/issues"}}