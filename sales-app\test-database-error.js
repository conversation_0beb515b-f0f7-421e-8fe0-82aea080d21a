// ===== اختبار أخطاء قاعدة البيانات =====

const fs = require('fs');
const path = require('path');

console.log('🧪 اختبار معالجة أخطاء قاعدة البيانات...');

function testDatabaseError() {
  const dbPath = path.join(__dirname, 'src', 'data', 'sales.db');
  const backupPath = path.join(__dirname, 'src', 'data', 'sales.db.backup');
  
  try {
    // 1. إنشاء نسخة احتياطية من قاعدة البيانات
    if (fs.existsSync(dbPath)) {
      console.log('📋 إنشاء نسخة احتياطية من قاعدة البيانات...');
      fs.copyFileSync(dbPath, backupPath);
      console.log('✅ تم إنشاء النسخة الاحتياطية');
    }
    
    // 2. محاكاة خطأ في قاعدة البيانات (حذف الملف)
    console.log('❌ محاكاة خطأ في قاعدة البيانات (حذف الملف)...');
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
      console.log('🗑️ تم حذف ملف قاعدة البيانات');
    }
    
    // 3. إنشاء ملف فاسد
    console.log('💥 إنشاء ملف قاعدة بيانات فاسد...');
    fs.writeFileSync(dbPath, 'هذا ملف فاسد وليس قاعدة بيانات صحيحة');
    console.log('📝 تم إنشاء ملف فاسد');
    
    console.log('');
    console.log('🎯 الآن يمكنك تشغيل التطبيق لرؤية معالجة الأخطاء:');
    console.log('   node_modules\\electron\\dist\\electron.exe .');
    console.log('');
    console.log('📋 ما ستراه:');
    console.log('   ✅ شاشة تحميل أثناء محاولة الاتصال');
    console.log('   ❌ إشعار خطأ في قاعدة البيانات');
    console.log('   🔄 محاولة إعادة الاتصال كل 5 ثوان');
    console.log('   📱 إشعار نظام التشغيل');
    console.log('   💬 نافذة حوار الخطأ');
    console.log('');
    console.log('🔧 لاستعادة قاعدة البيانات الصحيحة، شغل:');
    console.log('   node restore-database.js');
    
  } catch (error) {
    console.error('❌ خطأ في اختبار أخطاء قاعدة البيانات:', error);
  }
}

// تشغيل الاختبار
testDatabaseError();
