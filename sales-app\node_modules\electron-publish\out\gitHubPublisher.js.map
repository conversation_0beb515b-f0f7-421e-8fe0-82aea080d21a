{"version": 3, "file": "gitHubPublisher.js", "sourceRoot": "", "sources": ["../src/gitHubPublisher.ts"], "names": [], "mappings": ";;;AAAA,+CAAiH;AACjH,+DAAmG;AAEnG,wEAAgE;AAEhE,uCAA+B;AAC/B,6BAA4B;AAC5B,6BAA2D;AAC3D,2CAAqF;AAmBrF,MAAa,eAAgB,SAAQ,yBAAa;IAYhD,YACE,OAAuB,EACN,IAAmB,EACnB,OAAe,EACf,UAA0B,EAAE;QAE7C,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAJH,SAAI,GAAJ,IAAI,CAAe;QACnB,YAAO,GAAP,OAAO,CAAQ;QACf,YAAO,GAAP,OAAO,CAAqB;QAdtC,aAAQ,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAA;QAIjH,iBAAY,GAAG,QAAQ,CAAA;QAIxB,qBAAgB,GAAkB,IAAI,CAAA;QAU5C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACtB,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA;YACxD,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,wCAAyB,CAAC,6FAA6F,CAAC,CAAA;YACpI,CAAC;YAED,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;YAEpB,IAAI,CAAC,IAAA,+BAAgB,EAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,wCAAyB,CAAC,iCAAiC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAA;YACzJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAElB,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,wCAAyB,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAA;QACpF,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAA;QAEpE,IAAI,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAA;YAC1B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,EAAE,8CAA8C,CAAC,CAAA;QACrG,CAAC;aAAM,IAAI,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,uEAAuE,EAAE,CAAC;YAC/J,IAAI,CAAC,WAAW,GAAG,YAAY,CAAA;YAC/B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,mCAAmC,EAAE,EAAE,mDAAmD,CAAC,CAAA;QAChH,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACrC,CAAC;aAAM,IAAK,OAAe,CAAC,UAAU,EAAE,CAAC;YACvC,IAAI,CAAC,WAAW,GAAG,YAAY,CAAA;QACjC,CAAC;aAAM,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,WAAW,GAAI,OAAe,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAA;QAC3E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,SAAS,GAAG;YAChB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAA;QAED,oIAAoI;QACpI,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAiB,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC7H,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1E,SAAQ;YACV,CAAC;YAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,OAAO,OAAO,CAAA;YAChB,CAAC;YAED,oEAAoE;YACpE,oEAAoE;YACpE,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC,gBAAgB,GAAG;oBACtB,MAAM,EAAE,mDAAmD;oBAC3D,GAAG,SAAS;oBACZ,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;oBAC5D,cAAc,EAAE,IAAI,CAAC,WAAW;iBACjC,CAAA;gBACD,kBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAA;gBAC7D,OAAO,IAAI,CAAA;YACb,CAAC;YAED,oEAAoE;YACpE,oEAAoE;YACpE,iDAAiD;YACjD,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;YAC1F,IAAI,CAAC,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;gBACnH,2FAA2F;gBAC3F,IAAI,CAAC,gBAAgB,GAAG;oBACtB,MAAM,EAAE,kDAAkD;oBAC1D,GAAG,SAAS;oBACZ,IAAI,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;iBACvC,CAAA;gBACD,kBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAA;gBAC7D,OAAO,IAAI,CAAA;YACb,CAAC;YACD,OAAO,OAAO,CAAA;QAChB,CAAC;QAED,oEAAoE;QACpE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAA,oBAAQ,GAAE,IAAI,IAAI,EAAE,CAAC;YAC5D,kBAAG,CAAC,IAAI,CACN;gBACE,MAAM,EAAE,uBAAuB;gBAC/B,GAAG,SAAS;aACb,EACD,yBAAyB,CAC1B,CAAA;YACD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;QAC7B,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG;YACtB,MAAM,EAAE,kGAAkG;YAC1G,GAAG,SAAS;SACb,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,OAAgB;QAChE,oCAAoC;QACpC,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,0BAA0B,EAAE,EAAE,0BAA0B,CAAC,CAAA;QAE5F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAe,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,aAAa,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACpJ,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAO,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,oBAAoB,KAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;gBACrI,OAAM;YACR,CAAC;QACH,CAAC;QAED,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,qBAAqB,EAAE,EAAE,wBAAwB,CAAC,CAAA;IACxF,CAAC;IAES,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,IAAU,EAAE,UAAkB,EAAE,gBAAkF;QAC3J,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAA;QACzC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,EAAE,oBAAoB,CAAC,CAAA;YAC5E,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAA,WAAQ,EAAC,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,QAAQ,EAAE,CAAC,CAAA;QAClH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAA;IAC/F,CAAC;IAEO,YAAY,CAClB,aAAqB,EACrB,SAA6B,EAC7B,QAAgB,EAChB,UAAkB,EAClB,gBAAkF,EAClF,OAAY;QAEZ,OAAO,+BAAY;aAChB,YAAY,CACX,IAAA,8CAAuB,EACrB;YACE,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,MAAM,EAAE,gCAAgC;gBACxC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,0BAA0B;gBACpE,gBAAgB,EAAE,UAAU;aAC7B;YACD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;SACxC,EACD,IAAI,CAAC,KAAK,CACX,EACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,gBAAgB,CACjB;aACA,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YAChB,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC1B,CAAC;iBAAM,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAA;YAC/J,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,CAAA;oBAC1C,UAAU,CAAC,GAAG,EAAE;wBACd,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC7H,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC,CAAA;gBAC7B,CAAC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,0BAA0B,CAAC,CAAM;QACvC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,KAAK,CAAA;QACd,CAAC;QACD,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAA;QAC1B,MAAM,yBAAyB,GAC7B,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QACtJ,OAAO,CAAC,CAAC,UAAU,KAAK,GAAG,IAAI,yBAAyB,CAAA;IAC1D,CAAC;IAEO,aAAa;QACnB,OAAO,IAAI,CAAC,aAAa,CAAU,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE;YACrG,QAAQ,EAAE,IAAI,CAAC,GAAG;YAClB,IAAI,EAAE,IAAI,CAAC,OAAO;YAClB,KAAK,EAAE,IAAI,CAAC,WAAW,KAAK,OAAO;YACnC,UAAU,EAAE,IAAI,CAAC,WAAW,KAAK,YAAY;SAC9C,CAAC,CAAA;IACJ,CAAC;IAED,YAAY;IACZ,oCAAoC;IACpC,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,aAAa,CAAU,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAC3I,CAAC;IAED,oCAAoC;IACpC,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC5B,OAAM;QACR,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAA;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,aAAa,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;YACnI,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,YAAY,gCAAS,EAAE,CAAC;oBAC3B,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;wBACzB,kBAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,uBAAuB,CAAC,CAAA;wBACrF,OAAM;oBACR,CAAC;yBAAM,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;wBACxD,SAAQ;oBACV,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,CAAA;YACT,CAAC;QACH,CAAC;QAED,kBAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,uBAAuB,CAAC,CAAA;IAC9D,CAAC;IAEO,aAAa,CAAI,IAAY,EAAE,KAAoB,EAAE,OAAuC,IAAI,EAAE,MAAiC;QACzI,yEAAyE;QACzE,MAAM,OAAO,GAAG,IAAA,WAAQ,EAAC,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,gBAAgB,EAAE,CAAC,CAAA;QACzE,OAAO,IAAA,gCAAS,EACd,+BAAY,CAAC,OAAO,CAClB,IAAA,8CAAuB,EACrB;YACE,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAW;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI;YAC7H,OAAO,EAAE,EAAE,MAAM,EAAE,gCAAgC,EAAE;YACrD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;SACxC,EACD,KAAK,EACL,MAAM,CACP,EACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,IAAI,CACL,CACF,CAAA;IACH,CAAC;IAED,QAAQ;QACN,OAAO,kBAAkB,IAAI,CAAC,IAAI,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,IAAI,cAAc,IAAI,CAAC,OAAO,GAAG,CAAA;IACnG,CAAC;CACF;AAhRD,0CAgRC", "sourcesContent": ["import { Arch, InvalidConfigurationError, isEmptyOrSpaces, isEnvTrue, isTokenCharValid, log } from \"builder-util\"\nimport { configureRequestOptions, GithubOptions, HttpError, parseJson } from \"builder-util-runtime\"\nimport { Fields } from \"builder-util/out/log\"\nimport { httpExecutor } from \"builder-util/out/nodeHttpExecutor\"\nimport { ClientRequest } from \"http\"\nimport { Lazy } from \"lazy-val\"\nimport * as mime from \"mime\"\nimport { parse as parseUrl, UrlWithStringQuery } from \"url\"\nimport { getCiTag, HttpPublisher, PublishContext, PublishOptions } from \"./publisher\"\n\nexport interface Release {\n  id: number\n  tag_name: string\n\n  draft: boolean\n  prerelease: boolean\n\n  published_at: string\n\n  upload_url: string\n}\n\ninterface Asset {\n  id: number\n  name: string\n}\n\nexport class GitHubPublisher extends HttpPublisher {\n  private readonly tag: string\n  readonly _release = new Lazy(() => (this.token === \"__test__\" ? Promise.resolve(null as any) : this.getOrCreateRelease()))\n\n  private readonly token: string\n\n  readonly providerName = \"github\"\n\n  private readonly releaseType: \"draft\" | \"prerelease\" | \"release\"\n\n  private releaseLogFields: Fields | null = null\n\n  constructor(\n    context: PublishContext,\n    private readonly info: GithubOptions,\n    private readonly version: string,\n    private readonly options: PublishOptions = {}\n  ) {\n    super(context, true)\n\n    let token = info.token\n    if (isEmptyOrSpaces(token)) {\n      token = process.env.GH_TOKEN || process.env.GITHUB_TOKEN\n      if (isEmptyOrSpaces(token)) {\n        throw new InvalidConfigurationError(`GitHub Personal Access Token is not set, neither programmatically, nor using env \"GH_TOKEN\"`)\n      }\n\n      token = token.trim()\n\n      if (!isTokenCharValid(token)) {\n        throw new InvalidConfigurationError(`GitHub Personal Access Token (${JSON.stringify(token)}) contains invalid characters, please check env \"GH_TOKEN\"`)\n      }\n    }\n\n    this.token = token\n\n    if (version.startsWith(\"v\")) {\n      throw new InvalidConfigurationError(`Version must not start with \"v\": ${version}`)\n    }\n\n    this.tag = info.vPrefixedTagName === false ? version : `v${version}`\n\n    if (isEnvTrue(process.env.EP_DRAFT)) {\n      this.releaseType = \"draft\"\n      log.info({ reason: \"env EP_DRAFT is set to true\" }, \"GitHub provider release type is set to draft\")\n    } else if (isEnvTrue(process.env.EP_PRE_RELEASE) || isEnvTrue(process.env.EP_PRELEASE) /* https://github.com/electron-userland/electron-builder/issues/2878 */) {\n      this.releaseType = \"prerelease\"\n      log.info({ reason: \"env EP_PRE_RELEASE is set to true\" }, \"GitHub provider release type is set to prerelease\")\n    } else if (info.releaseType != null) {\n      this.releaseType = info.releaseType\n    } else if ((options as any).prerelease) {\n      this.releaseType = \"prerelease\"\n    } else {\n      // noinspection PointlessBooleanExpressionJS\n      this.releaseType = (options as any).draft === false ? \"release\" : \"draft\"\n    }\n  }\n\n  private async getOrCreateRelease(): Promise<Release | null> {\n    const logFields = {\n      tag: this.tag,\n      version: this.version,\n    }\n\n    // we don't use \"Get a release by tag name\" because \"tag name\" means existing git tag, but we draft release and don't create git tag\n    const releases = await this.githubRequest<Array<Release>>(`/repos/${this.info.owner}/${this.info.repo}/releases`, this.token)\n    for (const release of releases) {\n      if (!(release.tag_name === this.tag || release.tag_name === this.version)) {\n        continue\n      }\n\n      if (release.draft) {\n        return release\n      }\n\n      // https://github.com/electron-userland/electron-builder/issues/1197\n      // https://github.com/electron-userland/electron-builder/issues/2072\n      if (this.releaseType === \"draft\") {\n        this.releaseLogFields = {\n          reason: \"existing type not compatible with publishing type\",\n          ...logFields,\n          existingType: release.prerelease ? \"pre-release\" : \"release\",\n          publishingType: this.releaseType,\n        }\n        log.warn(this.releaseLogFields, \"GitHub release not created\")\n        return null\n      }\n\n      // https://github.com/electron-userland/electron-builder/issues/1133\n      // https://github.com/electron-userland/electron-builder/issues/2074\n      // if release created < 2 hours — allow to upload\n      const publishedAt = release.published_at == null ? null : Date.parse(release.published_at)\n      if (!isEnvTrue(process.env.EP_GH_IGNORE_TIME) && publishedAt != null && Date.now() - publishedAt > 2 * 3600 * 1000) {\n        // https://github.com/electron-userland/electron-builder/issues/1183#issuecomment-275867187\n        this.releaseLogFields = {\n          reason: \"existing release published more than 2 hours ago\",\n          ...logFields,\n          date: new Date(publishedAt).toString(),\n        }\n        log.warn(this.releaseLogFields, \"GitHub release not created\")\n        return null\n      }\n      return release\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/1835\n    if (this.options.publish === \"always\" || getCiTag() != null) {\n      log.info(\n        {\n          reason: \"release doesn't exist\",\n          ...logFields,\n        },\n        `creating GitHub release`\n      )\n      return this.createRelease()\n    }\n\n    this.releaseLogFields = {\n      reason: 'release doesn\\'t exist and not created because \"publish\" is not \"always\" and build is not on tag',\n      ...logFields,\n    }\n    return null\n  }\n\n  private async overwriteArtifact(fileName: string, release: Release) {\n    // delete old artifact and re-upload\n    log.warn({ file: fileName, reason: \"already exists on GitHub\" }, \"overwrite published file\")\n\n    const assets = await this.githubRequest<Array<Asset>>(`/repos/${this.info.owner}/${this.info.repo}/releases/${release.id}/assets`, this.token, null)\n    for (const asset of assets) {\n      if (asset.name === fileName) {\n        await this.githubRequest<void>(`/repos/${this.info.owner}/${this.info.repo}/releases/assets/${asset.id}`, this.token, null, \"DELETE\")\n        return\n      }\n    }\n\n    log.debug({ file: fileName, reason: \"not found on GitHub\" }, \"trying to upload again\")\n  }\n\n  protected async doUpload(fileName: string, arch: Arch, dataLength: number, requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void): Promise<any> {\n    const release = await this._release.value\n    if (release == null) {\n      log.warn({ file: fileName, ...this.releaseLogFields }, \"skipped publishing\")\n      return\n    }\n\n    const parsedUrl = parseUrl(`${release.upload_url.substring(0, release.upload_url.indexOf(\"{\"))}?name=${fileName}`)\n    return await this.doUploadFile(0, parsedUrl, fileName, dataLength, requestProcessor, release)\n  }\n\n  private doUploadFile(\n    attemptNumber: number,\n    parsedUrl: UrlWithStringQuery,\n    fileName: string,\n    dataLength: number,\n    requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void,\n    release: any\n  ): Promise<any> {\n    return httpExecutor\n      .doApiRequest(\n        configureRequestOptions(\n          {\n            protocol: parsedUrl.protocol,\n            hostname: parsedUrl.hostname,\n            path: parsedUrl.path,\n            method: \"POST\",\n            headers: {\n              accept: \"application/vnd.github.v3+json\",\n              \"Content-Type\": mime.getType(fileName) || \"application/octet-stream\",\n              \"Content-Length\": dataLength,\n            },\n            timeout: this.info.timeout || undefined,\n          },\n          this.token\n        ),\n        this.context.cancellationToken,\n        requestProcessor\n      )\n      .catch((e: any) => {\n        if (attemptNumber > 3) {\n          return Promise.reject(e)\n        } else if (this.doesErrorMeanAlreadyExists(e)) {\n          return this.overwriteArtifact(fileName, release).then(() => this.doUploadFile(attemptNumber + 1, parsedUrl, fileName, dataLength, requestProcessor, release))\n        } else {\n          return new Promise((resolve, reject) => {\n            const newAttemptNumber = attemptNumber + 1\n            setTimeout(() => {\n              this.doUploadFile(newAttemptNumber, parsedUrl, fileName, dataLength, requestProcessor, release).then(resolve).catch(reject)\n            }, newAttemptNumber * 2000)\n          })\n        }\n      })\n  }\n\n  private doesErrorMeanAlreadyExists(e: any) {\n    if (!e.description) {\n      return false\n    }\n    const desc = e.description\n    const descIncludesAlreadyExists =\n      (desc.includes(\"errors\") && desc.includes(\"already_exists\")) || (desc.errors && desc.errors.length >= 1 && desc.errors[0].code === \"already_exists\")\n    return e.statusCode === 422 && descIncludesAlreadyExists\n  }\n\n  private createRelease() {\n    return this.githubRequest<Release>(`/repos/${this.info.owner}/${this.info.repo}/releases`, this.token, {\n      tag_name: this.tag,\n      name: this.version,\n      draft: this.releaseType === \"draft\",\n      prerelease: this.releaseType === \"prerelease\",\n    })\n  }\n\n  // test only\n  //noinspection JSUnusedGlobalSymbols\n  async getRelease(): Promise<any> {\n    return this.githubRequest<Release>(`/repos/${this.info.owner}/${this.info.repo}/releases/${(await this._release.value)!.id}`, this.token)\n  }\n\n  //noinspection JSUnusedGlobalSymbols\n  async deleteRelease(): Promise<any> {\n    if (!this._release.hasValue) {\n      return\n    }\n\n    const release = await this._release.value\n    for (let i = 0; i < 3; i++) {\n      try {\n        return await this.githubRequest(`/repos/${this.info.owner}/${this.info.repo}/releases/${release.id}`, this.token, null, \"DELETE\")\n      } catch (e: any) {\n        if (e instanceof HttpError) {\n          if (e.statusCode === 404) {\n            log.warn({ releaseId: release.id, reason: \"doesn't exist\" }, \"cannot delete release\")\n            return\n          } else if (e.statusCode === 405 || e.statusCode === 502) {\n            continue\n          }\n        }\n\n        throw e\n      }\n    }\n\n    log.warn({ releaseId: release.id }, \"cannot delete release\")\n  }\n\n  private githubRequest<T>(path: string, token: string | null, data: { [name: string]: any } | null = null, method?: \"GET\" | \"DELETE\" | \"PUT\"): Promise<T> {\n    // host can contains port, but node http doesn't support host as url does\n    const baseUrl = parseUrl(`https://${this.info.host || \"api.github.com\"}`)\n    return parseJson(\n      httpExecutor.request(\n        configureRequestOptions(\n          {\n            protocol: baseUrl.protocol,\n            hostname: baseUrl.hostname,\n            port: baseUrl.port as any,\n            path: this.info.host != null && this.info.host !== \"github.com\" ? `/api/v3${path.startsWith(\"/\") ? path : `/${path}`}` : path,\n            headers: { accept: \"application/vnd.github.v3+json\" },\n            timeout: this.info.timeout || undefined,\n          },\n          token,\n          method\n        ),\n        this.context.cancellationToken,\n        data\n      )\n    )\n  }\n\n  toString() {\n    return `Github (owner: ${this.info.owner}, project: ${this.info.repo}, version: ${this.version})`\n  }\n}\n"]}