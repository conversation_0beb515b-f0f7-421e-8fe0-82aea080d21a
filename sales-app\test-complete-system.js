// ===== اختبار شامل للنظام =====

const SalesDatabase = require('./src/js/database');

console.log('🚀 بدء الاختبار الشامل للنظام...');

async function testCompleteSystem() {
  try {
    // إنشاء قاعدة البيانات
    const database = new SalesDatabase();
    
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // 1. اختبار العملاء
    console.log('\n📋 اختبار العملاء:');
    const customers = database.getAllCustomers();
    console.log(`   👥 إجمالي العملاء: ${customers.length}`);
    
    if (customers.length > 0) {
      console.log(`   👤 أول عميل: ${customers[0].full_name} (${customers[0].region})`);
      console.log(`   👤 آخر عميل: ${customers[customers.length-1].full_name} (${customers[customers.length-1].region})`);
    }
    
    // 2. اختبار المنتجات
    console.log('\n📦 اختبار المنتجات:');
    const products = database.getAllProducts();
    console.log(`   🛍️ إجمالي المنتجات: ${products.length}`);
    
    // تجميع حسب الفئة
    const categories = {};
    products.forEach(product => {
      if (!categories[product.category]) {
        categories[product.category] = { count: 0, totalValue: 0 };
      }
      categories[product.category].count++;
      categories[product.category].totalValue += product.price * product.stock;
    });
    
    console.log('   📊 المنتجات حسب الفئة:');
    Object.entries(categories).forEach(([category, data]) => {
      console.log(`      🏷️ ${category}: ${data.count} منتج (قيمة: ${data.totalValue.toLocaleString()} د.ع)`);
    });
    
    // 3. اختبار إنشاء فاتورة تجريبية
    console.log('\n💰 اختبار إنشاء فاتورة:');
    if (customers.length > 0 && products.length > 0) {
      const testCustomer = customers[Math.floor(Math.random() * customers.length)];
      const testProducts = products.slice(0, 3); // أول 3 منتجات
      
      const testInvoice = {
        invoiceNumber: `TEST-${Date.now()}`,
        customerId: testCustomer.id,
        customerName: testCustomer.full_name,
        total: 0,
        paidAmount: 0,
        remainingAmount: 0,
        status: 'paid',
        paymentMethod: 'cash',
        notes: 'فاتورة اختبار شاملة',
        items: []
      };
      
      // إضافة منتجات للفاتورة
      testProducts.forEach(product => {
        const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 قطع
        const itemTotal = product.price * quantity;
        
        testInvoice.items.push({
          productId: product.id,
          name: product.name,
          quantity: quantity,
          price: product.price,
          total: itemTotal
        });
        
        testInvoice.total += itemTotal;
      });
      
      testInvoice.paidAmount = testInvoice.total;
      
      console.log(`   📄 إنشاء فاتورة للعميل: ${testCustomer.full_name}`);
      console.log(`   💵 إجمالي الفاتورة: ${testInvoice.total.toLocaleString()} د.ع`);
      console.log(`   📦 عدد المنتجات: ${testInvoice.items.length}`);
      
      // حفظ الفاتورة
      const invoiceId = database.saveInvoice(testInvoice);
      console.log(`   ✅ تم حفظ الفاتورة برقم: ${invoiceId}`);
      
      // اختبار جلب فواتير العميل
      const customerInvoices = database.getCustomerInvoices(testCustomer.id);
      console.log(`   📋 عدد فواتير العميل: ${customerInvoices.length}`);
    }
    
    // 4. اختبار الإحصائيات
    console.log('\n📊 اختبار الإحصائيات:');
    const stats = database.getDailyStats();
    console.log(`   📈 فواتير اليوم: ${stats.total_invoices}`);
    console.log(`   💰 مبيعات اليوم: ${stats.total_sales.toLocaleString()} د.ع`);
    console.log(`   📊 متوسط الفاتورة: ${stats.average_sale.toLocaleString()} د.ع`);
    
    // 5. اختبار المنتجات منخفضة المخزون
    console.log('\n⚠️ اختبار المنتجات منخفضة المخزون:');
    const lowStock = database.getLowStockProducts(50);
    console.log(`   📉 منتجات تحتاج تجديد: ${lowStock.length}`);
    
    if (lowStock.length > 0) {
      console.log('   📋 قائمة المنتجات:');
      lowStock.slice(0, 5).forEach(product => {
        console.log(`      ⚠️ ${product.name}: ${product.stock} ${product.unit} (${product.price.toLocaleString()} د.ع)`);
      });
      if (lowStock.length > 5) {
        console.log(`      ... و ${lowStock.length - 5} منتج آخر`);
      }
    }
    
    // 6. اختبار أغلى وأرخص المنتجات
    console.log('\n💎 اختبار أسعار المنتجات:');
    const sortedByPrice = [...products].sort((a, b) => b.price - a.price);
    console.log(`   💰 أغلى منتج: ${sortedByPrice[0].name} (${sortedByPrice[0].price.toLocaleString()} د.ع)`);
    console.log(`   💵 أرخص منتج: ${sortedByPrice[sortedByPrice.length-1].name} (${sortedByPrice[sortedByPrice.length-1].price.toLocaleString()} د.ع)`);
    
    // 7. حساب إجمالي قيمة المخزون
    console.log('\n🏪 اختبار قيمة المخزون:');
    const totalInventoryValue = products.reduce((total, product) => {
      return total + (product.price * product.stock);
    }, 0);
    console.log(`   💰 إجمالي قيمة المخزون: ${totalInventoryValue.toLocaleString()} د.ع`);
    
    // 8. توزيع العملاء حسب المناطق
    console.log('\n🗺️ توزيع العملاء:');
    const regions = {};
    customers.forEach(customer => {
      regions[customer.region] = (regions[customer.region] || 0) + 1;
    });
    
    const topRegions = Object.entries(regions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
    
    console.log('   📍 أكثر 5 مناطق:');
    topRegions.forEach(([region, count]) => {
      console.log(`      🏙️ ${region}: ${count} عميل`);
    });
    
    // إغلاق قاعدة البيانات
    database.close();
    
    // 9. ملخص النتائج
    console.log('\n🎉 ملخص الاختبار الشامل:');
    console.log('═══════════════════════════════════════');
    console.log(`✅ العملاء: ${customers.length} عميل من ${Object.keys(regions).length} منطقة`);
    console.log(`✅ المنتجات: ${products.length} منتج في ${Object.keys(categories).length} فئة`);
    console.log(`✅ الفواتير: ${stats.total_invoices} فاتورة اليوم`);
    console.log(`✅ المبيعات: ${stats.total_sales.toLocaleString()} د.ع`);
    console.log(`✅ قيمة المخزون: ${totalInventoryValue.toLocaleString()} د.ع`);
    console.log(`⚠️ منتجات منخفضة المخزون: ${lowStock.length}`);
    console.log('═══════════════════════════════════════');
    console.log('🎯 النظام جاهز للاستخدام في نقطة البيع!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار الشامل:', error);
  }
}

// تشغيل الاختبار الشامل
testCompleteSystem();
