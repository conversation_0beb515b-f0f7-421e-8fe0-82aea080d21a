// ===== إدارة التنقل والصفحات =====

// تحديث وظيفة التنقل للعمل بدون iframe
function navigateToPage(page) {
  console.log(`📄 محاولة التنقل إلى صفحة: ${page}`);

  // إزالة الفئة النشطة من الرابط الحالي
  const currentActiveLink = document.querySelector('.nav-item.active');
  if (currentActiveLink) {
    currentActiveLink.classList.remove('active');
  }

  // إضافة الفئة النشطة للرابط الجديد
  const newActiveLink = document.querySelector(`[data-page="${page}"]`);
  if (newActiveLink) {
    newActiveLink.closest('.nav-item').classList.add('active');
  }

  // تحديث عنوان الصفحة
  updatePageTitle(page);

  // تحميل محتوى الصفحة مباشرة
  loadPageContent(page);

  console.log(`✅ تم التنقل إلى صفحة: ${page}`);
}

// تحميل محتوى الصفحة
async function loadPageContent(page) {
  const contentArea = document.getElementById('contentArea');
  if (!contentArea) {
    console.error('❌ منطقة المحتوى غير موجودة');
    return;
  }

  try {
    // إظهار مؤشر التحميل
    contentArea.innerHTML = `
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>جاري تحميل الصفحة...</p>
      </div>
    `;

    // تحميل محتوى الصفحة حسب النوع
    let content = '';

    switch(page) {
      case 'dashboard':
        content = await loadDashboardContent();
        break;
      case 'customers':
        content = await loadCustomersContent();
        break;
      case 'sales':
        content = createSalesPage();
        break;
      case 'inventory':
        content = createInventoryPage();
        break;
      case 'products':
        content = createProductsPage();
        break;
      case 'reports':
        content = createReportsPage();
        break;
      case 'settings':
        content = createSettingsPage();
        break;
      default:
        content = createDefaultPage(page);
    }

    // عرض المحتوى
    contentArea.innerHTML = content;

    // تهيئة الصفحة المحملة
    initializePage(page);

  } catch (error) {
    console.error(`❌ خطأ في تحميل صفحة ${page}:`, error);
    contentArea.innerHTML = `
      <div class="error-container">
        <span class="material-icons">error</span>
        <h3>خطأ في تحميل الصفحة</h3>
        <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
        <button class="btn btn-primary" onclick="navigateToPage('${page}')">إعادة المحاولة</button>
      </div>
    `;
  }
}

function updatePageTitle(page) {
  const pageTitle = document.getElementById('pageTitle');
  const titles = {
    dashboard: 'لوحة التحكم',
    sales: 'المبيعات والفواتير',
    inventory: 'إدارة المخزون',
    customers: 'العملاء والموردين',
    reports: 'التقارير المالية',
    products: 'إدارة المنتجات',
    settings: 'الإعدادات'
  };

  if (pageTitle && titles[page]) {
    pageTitle.textContent = titles[page];
  }
}

// وظيفة ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
}

// تحميل محتوى لوحة التحكم
async function loadDashboardContent() {
  return `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>لوحة التحكم</h1>
        <p>نظرة عامة على أداء المبيعات</p>
      </div>

      <div class="dashboard-stats">
        <div class="stat-card primary">
          <div class="stat-icon">
            <span class="material-icons">trending_up</span>
          </div>
          <div class="stat-info">
            <h3>مبيعات اليوم</h3>
            <p class="stat-value">12,500 د.ع</p>
            <span class="stat-change positive">+15%</span>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <span class="material-icons">people</span>
          </div>
          <div class="stat-info">
            <h3>العملاء</h3>
            <p class="stat-value">84</p>
            <span class="stat-change positive">+5 جديد</span>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <span class="material-icons">inventory</span>
          </div>
          <div class="stat-info">
            <h3>المنتجات</h3>
            <p class="stat-value">156</p>
            <span class="stat-change neutral">في المخزون</span>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <span class="material-icons">receipt</span>
          </div>
          <div class="stat-info">
            <h3>الفواتير</h3>
            <p class="stat-value">23</p>
            <span class="stat-change positive">اليوم</span>
          </div>
        </div>
      </div>

      <div class="dashboard-content">
        <div class="quick-actions">
          <h3>إجراءات سريعة</h3>
          <div class="actions-grid">
            <button class="action-btn" onclick="navigateToPage('sales')">
              <span class="material-icons">add_shopping_cart</span>
              <span>فاتورة جديدة</span>
            </button>
            <button class="action-btn" onclick="navigateToPage('customers')">
              <span class="material-icons">person_add</span>
              <span>إضافة عميل</span>
            </button>
            <button class="action-btn" onclick="navigateToPage('products')">
              <span class="material-icons">add_box</span>
              <span>إضافة منتج</span>
            </button>
            <button class="action-btn" onclick="navigateToPage('reports')">
              <span class="material-icons">assessment</span>
              <span>عرض التقارير</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `;
}

// تحميل محتوى صفحة العملاء
async function loadCustomersContent() {
  return `
    <div class="customers-container">
      <!-- شريط الأدوات العلوي -->
      <div class="customers-header">
        <div class="header-content">
          <h1>إدارة العملاء</h1>
          <p>إضافة وإدارة بيانات العملاء</p>
        </div>
        <button class="btn btn-primary" onclick="addNewCustomer()">
          <span class="material-icons">person_add</span>
          إضافة عميل جديد
        </button>
      </div>

      <!-- شريط البحث والفلترة -->
      <div class="search-filter-bar">
        <div class="search-box">
          <span class="material-icons">search</span>
          <input type="text" id="customerSearch" placeholder="البحث عن عميل..." onkeyup="searchCustomers()">
        </div>
        <div class="filter-box">
          <select id="customerFilter" onchange="filterCustomers()">
            <option value="all">جميع العملاء</option>
            <option value="individual">أفراد</option>
            <option value="company">شركات</option>
          </select>
        </div>
      </div>

      <!-- عدد العملاء -->
      <div class="customers-count">
        <span id="customersCount">0 عميل</span>
      </div>

      <!-- قائمة العملاء على شكل كروت -->
      <div class="customers-grid" id="customersGrid">
        <!-- سيتم إضافة كروت العملاء هنا ديناميكياً -->
      </div>
    </div>
  `;
}

// تهيئة الصفحة المحملة
function initializePage(page) {
  console.log(`🔧 تهيئة صفحة: ${page}`);

  switch(page) {
    case 'dashboard':
      // تهيئة لوحة التحكم
      break;
    case 'customers':
      // تهيئة صفحة العملاء
      if (typeof initializeCustomersPage === 'function') {
        initializeCustomersPage();
      }
      break;
    case 'sales':
      // تهيئة صفحة المبيعات
      break;
    case 'products':
      // تهيئة صفحة المنتجات
      break;
    default:
      console.log(`لا توجد تهيئة خاصة لصفحة: ${page}`);
  }
}

// ===== قوالب الصفحات (للاستخدام المستقبلي) =====
function createSalesPage() {
  return `
    <div class="page-header">
      <h2>المبيعات والفواتير</h2>
      <div class="page-actions">
        <button class="btn btn-primary" onclick="createNewInvoice()">
          <span class="material-icons">add</span>
          فاتورة جديدة
        </button>
      </div>
    </div>

    <div class="sales-dashboard">
      <div class="sales-stats">
        <div class="stat-card primary">
          <div class="stat-icon">
            <span class="material-icons">receipt_long</span>
          </div>
          <div class="stat-info">
            <h3>فواتير اليوم</h3>
            <p class="stat-value">23</p>
            <span class="stat-change positive">+5 من أمس</span>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <span class="material-icons">payments</span>
          </div>
          <div class="stat-info">
            <h3>إجمالي المبيعات</h3>
            <p class="stat-value">45,230 ريال</p>
            <span class="stat-change positive">+12.5%</span>
          </div>
        </div>
      </div>

      <div class="recent-invoices">
        <h3>الفواتير الحديثة</h3>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>#INV-001</td>
                <td>أحمد محمد</td>
                <td>1,250 ريال</td>
                <td>اليوم</td>
                <td><span class="badge badge-success">مدفوعة</span></td>
                <td>
                  <button class="btn-icon" title="عرض">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="btn-icon" title="طباعة">
                    <span class="material-icons">print</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `;
}

function createInventoryPage() {
  return `
    <div class="page-header">
      <h2>إدارة المخزون</h2>
      <div class="page-actions">
        <button class="btn btn-primary" onclick="addNewProduct()">
          <span class="material-icons">add</span>
          إضافة منتج
        </button>
        <button class="btn btn-secondary" onclick="importProducts()">
          <span class="material-icons">upload</span>
          استيراد منتجات
        </button>
      </div>
    </div>

    <div class="inventory-dashboard">
      <div class="inventory-stats">
        <div class="stat-card info">
          <div class="stat-icon">
            <span class="material-icons">inventory_2</span>
          </div>
          <div class="stat-info">
            <h3>إجمالي المنتجات</h3>
            <p class="stat-value">1,247</p>
            <span class="stat-change neutral">في المخزون</span>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <span class="material-icons">warning</span>
          </div>
          <div class="stat-info">
            <h3>مخزون منخفض</h3>
            <p class="stat-value">12</p>
            <span class="stat-change negative">يحتاج إعادة تموين</span>
          </div>
        </div>
      </div>

      <div class="inventory-filters">
        <div class="filter-group">
          <label>البحث:</label>
          <input type="text" class="form-control" placeholder="ابحث عن منتج...">
        </div>
        <div class="filter-group">
          <label>الفئة:</label>
          <select class="form-control">
            <option>جميع الفئات</option>
            <option>أدوات كهربائية</option>
            <option>مواد بناء</option>
            <option>سباكة</option>
          </select>
        </div>
      </div>

      <div class="products-grid">
        <div class="product-card">
          <div class="product-image">
            <span class="material-icons">electrical_services</span>
          </div>
          <div class="product-info">
            <h4>كابل كهربائي 2.5 مم</h4>
            <p class="product-price">25 ريال/متر</p>
            <p class="product-stock">المخزون: 150 متر</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createCustomersPage() {
  return `
    <div class="page-header">
      <h2>العملاء والموردين</h2>
      <div class="page-actions">
        <button class="btn btn-primary" onclick="addNewCustomer()">
          <span class="material-icons">person_add</span>
          عميل جديد
        </button>
        <button class="btn btn-secondary" onclick="addNewSupplier()">
          <span class="material-icons">business</span>
          مورد جديد
        </button>
      </div>
    </div>

    <div class="customers-dashboard">
      <div class="customers-tabs">
        <button class="tab-btn active" onclick="switchTab('customers')">العملاء</button>
        <button class="tab-btn" onclick="switchTab('suppliers')">الموردين</button>
      </div>

      <div class="customers-stats">
        <div class="stat-card primary">
          <div class="stat-icon">
            <span class="material-icons">people</span>
          </div>
          <div class="stat-info">
            <h3>إجمالي العملاء</h3>
            <p class="stat-value">342</p>
            <span class="stat-change positive">+15 هذا الشهر</span>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <span class="material-icons">star</span>
          </div>
          <div class="stat-info">
            <h3>عملاء مميزون</h3>
            <p class="stat-value">28</p>
            <span class="stat-change positive">عملاء VIP</span>
          </div>
        </div>
      </div>

      <div class="customers-list">
        <div class="search-bar">
          <input type="text" class="form-control" placeholder="البحث عن عميل...">
          <button class="btn btn-primary">
            <span class="material-icons">search</span>
          </button>
        </div>

        <div class="customers-table">
          <table class="table">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>إجمالي المشتريات</th>
                <th>آخر زيارة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>محمد أحمد السعيد</td>
                <td>0501234567</td>
                <td><EMAIL></td>
                <td>15,750 ريال</td>
                <td>اليوم</td>
                <td>
                  <button class="btn-icon" title="عرض">
                    <span class="material-icons">visibility</span>
                  </button>
                  <button class="btn-icon" title="تعديل">
                    <span class="material-icons">edit</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `;
}

function createReportsPage() {
  return `
    <div class="page-header">
      <h2>التقارير المالية</h2>
      <div class="page-actions">
        <button class="btn btn-primary" onclick="generateReport()">
          <span class="material-icons">assessment</span>
          إنشاء تقرير
        </button>
        <button class="btn btn-secondary" onclick="exportReport()">
          <span class="material-icons">download</span>
          تصدير
        </button>
      </div>
    </div>

    <div class="reports-dashboard">
      <div class="report-filters">
        <div class="filter-row">
          <div class="filter-group">
            <label>نوع التقرير:</label>
            <select class="form-control">
              <option>تقرير المبيعات</option>
              <option>تقرير الأرباح</option>
              <option>تقرير المخزون</option>
              <option>تقرير العملاء</option>
            </select>
          </div>
          <div class="filter-group">
            <label>الفترة:</label>
            <select class="form-control">
              <option>اليوم</option>
              <option>هذا الأسبوع</option>
              <option>هذا الشهر</option>
              <option>هذا العام</option>
              <option>فترة مخصصة</option>
            </select>
          </div>
        </div>
      </div>

      <div class="reports-summary">
        <div class="summary-card">
          <h3>ملخص المبيعات</h3>
          <div class="summary-stats">
            <div class="summary-item">
              <span class="label">إجمالي المبيعات:</span>
              <span class="value">125,750 ريال</span>
            </div>
            <div class="summary-item">
              <span class="label">عدد الفواتير:</span>
              <span class="value">89 فاتورة</span>
            </div>
            <div class="summary-item">
              <span class="label">متوسط الفاتورة:</span>
              <span class="value">1,413 ريال</span>
            </div>
          </div>
        </div>
      </div>

      <div class="charts-container">
        <div class="chart-card">
          <h3>مبيعات الأسبوع</h3>
          <div class="chart-placeholder">
            <span class="material-icons">bar_chart</span>
            <p>سيتم عرض الرسم البياني هنا</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createProductsPage() {
  return `
    <div class="page-header">
      <h2>إدارة المنتجات</h2>
      <div class="page-actions">
        <button class="btn btn-primary" onclick="addProduct()">
          <span class="material-icons">add</span>
          منتج جديد
        </button>
        <button class="btn btn-secondary" onclick="manageCategories()">
          <span class="material-icons">category</span>
          إدارة الفئات
        </button>
      </div>
    </div>

    <div class="products-dashboard">
      <div class="products-filters">
        <div class="search-section">
          <input type="text" class="form-control" placeholder="البحث عن منتج...">
          <button class="btn btn-primary">
            <span class="material-icons">search</span>
          </button>
        </div>

        <div class="filter-section">
          <select class="form-control">
            <option>جميع الفئات</option>
            <option>أدوات كهربائية</option>
            <option>مواد بناء</option>
            <option>سباكة</option>
            <option>دهانات</option>
          </select>
        </div>
      </div>

      <div class="products-view-toggle">
        <button class="view-btn active" onclick="switchView('grid')">
          <span class="material-icons">grid_view</span>
        </button>
        <button class="view-btn" onclick="switchView('list')">
          <span class="material-icons">view_list</span>
        </button>
      </div>

      <div class="products-container">
        <div class="product-item">
          <div class="product-image">
            <span class="material-icons">electrical_services</span>
          </div>
          <div class="product-details">
            <h4>كابل كهربائي 2.5 مم</h4>
            <p class="product-category">أدوات كهربائية</p>
            <p class="product-price">25 ريال/متر</p>
            <p class="product-stock">المخزون: 150 متر</p>
          </div>
          <div class="product-actions">
            <button class="btn-icon" title="تعديل">
              <span class="material-icons">edit</span>
            </button>
            <button class="btn-icon" title="حذف">
              <span class="material-icons">delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createSettingsPage() {
  return `
    <div class="page-header">
      <h2>الإعدادات</h2>
    </div>

    <div class="settings-dashboard">
      <div class="settings-tabs">
        <button class="tab-btn active" onclick="switchSettingsTab('general')">عام</button>
        <button class="tab-btn" onclick="switchSettingsTab('company')">بيانات الشركة</button>
        <button class="tab-btn" onclick="switchSettingsTab('users')">المستخدمين</button>
        <button class="tab-btn" onclick="switchSettingsTab('backup')">النسخ الاحتياطي</button>
      </div>

      <div class="settings-content">
        <div class="settings-section">
          <h3>الإعدادات العامة</h3>
          <div class="form-group">
            <label class="form-label">لغة النظام:</label>
            <select class="form-control">
              <option>العربية</option>
              <option>English</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">العملة الافتراضية:</label>
            <select class="form-control">
              <option>ريال سعودي (SAR)</option>
              <option>دولار أمريكي (USD)</option>
              <option>يورو (EUR)</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">المنطقة الزمنية:</label>
            <select class="form-control">
              <option>الرياض (GMT+3)</option>
              <option>القاهرة (GMT+2)</option>
              <option>دبي (GMT+4)</option>
            </select>
          </div>

          <button class="btn btn-primary">حفظ الإعدادات</button>
        </div>
      </div>
    </div>
  `;
}

function createDefaultPage(page) {
  return `
    <div class="page-header">
      <h2>صفحة ${page}</h2>
    </div>

    <div class="page-content">
      <div class="empty-state">
        <span class="material-icons">construction</span>
        <h3>قيد التطوير</h3>
        <p>هذه الصفحة قيد التطوير وستكون متاحة قريباً</p>
      </div>
    </div>
  `;
}

// ===== وظائف التفاعل =====
function createNewInvoice() {
  SalesApp.showNotification('سيتم فتح نافذة إنشاء فاتورة جديدة', 'info');
}

function addNewProduct() {
  SalesApp.showNotification('سيتم فتح نافذة إضافة منتج جديد', 'info');
}

function addNewCustomer() {
  SalesApp.showNotification('سيتم فتح نافذة إضافة عميل جديد', 'info');
}

function generateReport() {
  SalesApp.showNotification('جاري إنشاء التقرير...', 'info');
}

function switchTab(tab) {
  SalesApp.showNotification(`تم التبديل إلى تبويب ${tab}`, 'success');
}

function switchView(view) {
  SalesApp.showNotification(`تم التبديل إلى عرض ${view}`, 'success');
}
