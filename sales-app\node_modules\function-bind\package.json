{"name": "function-bind", "version": "1.1.2", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": "Raynos <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/Raynos/function-bind.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1"}, "license": "MIT", "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}